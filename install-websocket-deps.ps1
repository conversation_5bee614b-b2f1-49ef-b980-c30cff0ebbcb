# 🚀 Script d'Installation WebSocket - Laravel Reverb
# Installe les dépendances nécessaires pour les WebSockets

Write-Host "🔄 Installation des dépendances WebSocket..." -ForegroundColor Cyan

# Fonction pour vérifier si une commande existe
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Vérifier que npm est installé
if (-not (Test-Command "npm")) {
    Write-Host "❌ npm n'est pas installé. Veuillez installer Node.js d'abord." -ForegroundColor Red
    exit 1
}

Write-Host "✅ npm trouvé" -ForegroundColor Green

# Installation côté client (lorrelei)
Write-Host "`n📱 Installation dépendances côté CLIENT..." -ForegroundColor Yellow
if (Test-Path "lorrelei") {
    Set-Location "lorrelei"
    
    Write-Host "📦 Installation laravel-echo et pusher-js..." -ForegroundColor Cyan
    npm install laravel-echo pusher-js
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Dépendances client installées avec succès" -ForegroundColor Green
    } else {
        Write-Host "❌ Erreur lors de l'installation côté client" -ForegroundColor Red
    }
    
    Set-Location ".."
} else {
    Write-Host "⚠️ Dossier lorrelei/ non trouvé" -ForegroundColor Yellow
}

# Installation côté admin (admin_marchand_lorrelei)
Write-Host "`n🛠️ Installation dépendances côté ADMIN..." -ForegroundColor Yellow
if (Test-Path "admin_marchand_lorrelei") {
    Set-Location "admin_marchand_lorrelei"
    
    Write-Host "📦 Installation laravel-echo et pusher-js..." -ForegroundColor Cyan
    npm install laravel-echo pusher-js
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Dépendances admin installées avec succès" -ForegroundColor Green
    } else {
        Write-Host "❌ Erreur lors de l'installation côté admin" -ForegroundColor Red
    }
    
    Set-Location ".."
} else {
    Write-Host "⚠️ Dossier admin_marchand_lorrelei/ non trouvé" -ForegroundColor Yellow
}

# Vérification des variables d'environnement
Write-Host "`n🔧 Vérification de la configuration..." -ForegroundColor Yellow

$envFiles = @("lorrelei/.env", "admin_marchand_lorrelei/.env")
$requiredVars = @("VITE_REVERB_APP_KEY", "VITE_REVERB_HOST", "VITE_REVERB_PORT", "VITE_REVERB_SCHEME")

foreach ($envFile in $envFiles) {
    if (Test-Path $envFile) {
        Write-Host "📄 Vérification de $envFile..." -ForegroundColor Cyan
        $envContent = Get-Content $envFile
        
        $missingVars = @()
        foreach ($var in $requiredVars) {
            $found = $envContent | Where-Object { $_ -match "^$var=" }
            if (-not $found) {
                $missingVars += $var
            }
        }
        
        if ($missingVars.Count -eq 0) {
            Write-Host "✅ Toutes les variables Reverb sont configurées" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Variables manquantes dans $envFile :" -ForegroundColor Yellow
            foreach ($missing in $missingVars) {
                Write-Host "   - $missing" -ForegroundColor Red
            }
            
            Write-Host "`n💡 Ajoutez ces variables à $envFile :" -ForegroundColor Cyan
            Write-Host "VITE_REVERB_APP_KEY=local-key" -ForegroundColor Gray
            Write-Host "VITE_REVERB_HOST=localhost" -ForegroundColor Gray
            Write-Host "VITE_REVERB_PORT=8080" -ForegroundColor Gray
            Write-Host "VITE_REVERB_SCHEME=http" -ForegroundColor Gray
        }
    } else {
        Write-Host "⚠️ Fichier $envFile non trouvé" -ForegroundColor Yellow
    }
}

# Instructions finales
Write-Host "`n🎯 PROCHAINES ÉTAPES :" -ForegroundColor Cyan
Write-Host "1. 🔧 Vérifiez que Laravel Reverb est configuré côté backend" -ForegroundColor White
Write-Host "2. 🚀 Démarrez le serveur Reverb : php artisan reverb:start" -ForegroundColor White
Write-Host "3. 🌐 Démarrez les serveurs de développement :" -ForegroundColor White
Write-Host "   - Client : cd lorrelei && npm run dev" -ForegroundColor Gray
Write-Host "   - Admin : cd admin_marchand_lorrelei && npm run dev" -ForegroundColor Gray
Write-Host "4. 🧪 Testez les WebSockets selon le guide WEBSOCKET_TESTING_GUIDE.md" -ForegroundColor White

Write-Host "`n📚 DOCUMENTATION :" -ForegroundColor Cyan
Write-Host "- 📄 WEBSOCKET_IMPLEMENTATION.md - Détails de l'implémentation" -ForegroundColor White
Write-Host "- 🧪 WEBSOCKET_TESTING_GUIDE.md - Guide de test complet" -ForegroundColor White

Write-Host "`n✨ Installation terminée ! Les WebSockets sont prêts à être testés." -ForegroundColor Green
