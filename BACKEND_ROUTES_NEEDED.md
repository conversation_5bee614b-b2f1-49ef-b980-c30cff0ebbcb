# 🔧 Routes Backend Nécessaires pour WebSocket

## ❌ **ERREURS ACTUELLES**

### **1. Route Typing Manquante**
```
POST http://127.0.0.1:8001/api/disputes/typing 405 (Method Not Allowed)
POST http://127.0.0.1:8001/api/messages/typing 405 (Method Not Allowed)
```

**Status :** ✅ **TEMPORAIREMENT DÉSACTIVÉ** côté frontend pour éviter les erreurs

## 🛠️ **ROUTES À CRÉER CÔTÉ BACKEND**

### **1. Routes Typing (Indicateurs de Frappe)**

#### **Client - Messages**
```php
// routes/api.php
Route::post('/messages/typing', [MessageController::class, 'sendTyping'])
    ->middleware('auth:sanctum');
```

#### **Admin - Litiges**
```php
// routes/api.php  
Route::post('/disputes/typing', [DisputeController::class, 'sendTyping'])
    ->middleware('auth:sanctum');
```

### **2. Contrôleurs à Mettre à Jour**

#### **MessageController.php**
```php
public function sendTyping(Request $request)
{
    $request->validate([
        'conversation_id' => 'required|exists:conversations,id',
        'is_typing' => 'required|boolean'
    ]);

    // Diffuser l'événement de frappe
    broadcast(new UserTyping(
        $request->conversation_id,
        auth()->id(),
        auth()->user()->name,
        'client',
        $request->is_typing
    ));

    return response()->json(['success' => true]);
}
```

#### **DisputeController.php**
```php
public function sendTyping(Request $request)
{
    $request->validate([
        'dispute_id' => 'required|exists:disputes,id',
        'is_typing' => 'required|boolean'
    ]);

    // Diffuser l'événement de frappe
    broadcast(new UserTyping(
        $request->dispute_id,
        auth()->id(),
        auth()->user()->name,
        'admin',
        $request->is_typing
    ));

    return response()->json(['success' => true]);
}
```

### **3. Événements WebSocket à Créer**

#### **UserTyping.php**
```php
<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserTyping implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $channelId;
    public $userId;
    public $userName;
    public $userType;
    public $isTyping;

    public function __construct($channelId, $userId, $userName, $userType, $isTyping)
    {
        $this->channelId = $channelId;
        $this->userId = $userId;
        $this->userName = $userName;
        $this->userType = $userType;
        $this->isTyping = $isTyping;
    }

    public function broadcastOn()
    {
        // Pour conversations
        if ($this->userType === 'client') {
            return new PrivateChannel('conversation.' . $this->channelId);
        }
        
        // Pour litiges
        return new PrivateChannel('dispute.' . $this->channelId);
    }

    public function broadcastAs()
    {
        return 'user.typing';
    }

    public function broadcastWith()
    {
        return [
            'user_id' => $this->userId,
            'user_name' => $this->userName,
            'user_type' => $this->userType,
            'is_typing' => $this->isTyping,
        ];
    }
}
```

## 🔄 **RÉACTIVATION FRONTEND**

Une fois les routes backend créées, réactiver côté frontend :

### **1. Service Client**
```typescript
// lorrelei/resources/js/services/ReverbWebSocketService.ts
async sendTyping(conversationId: string, isTyping: boolean = true): Promise<void> {
    // Supprimer cette condition pour réactiver
    // if (process.env.NODE_ENV === 'development') {
    //     console.log(`[WebSocket] Typing ${isTyping ? 'started' : 'stopped'} for conversation ${conversationId}`);
    //     return;
    // }

    try {
        const response = await fetch('/api/messages/typing', {
            // ... reste du code
        });
    } catch (error) {
        console.error('Failed to send typing status:', error);
    }
}
```

### **2. Service Admin**
```typescript
// admin_marchand_lorrelei/resources/js/services/ReverbWebSocketService.ts
async sendTyping(disputeId: string, isTyping: boolean = true): Promise<void> {
    // Supprimer cette condition pour réactiver
    // if (process.env.NODE_ENV === 'development') {
    //     console.log(`[WebSocket] Typing ${isTyping ? 'started' : 'stopped'} for dispute ${disputeId}`);
    //     return;
    // }

    try {
        const response = await fetch('/api/disputes/typing', {
            // ... reste du code
        });
    } catch (error) {
        console.error('Failed to send typing status:', error);
    }
}
```

## ✅ **ÉTAT ACTUEL**

### **Fonctionnalités Actives**
- ✅ **Connexion WebSocket** - Fonctionne
- ✅ **Messages temps réel** - Fonctionne (si backend configuré)
- ✅ **Interface utilisateur** - Complète
- ✅ **Notifications toast** - Fonctionnent
- ✅ **Reconnexion automatique** - Fonctionne

### **Fonctionnalités Désactivées Temporairement**
- ⏸️ **Indicateurs de frappe** - Désactivés (routes manquantes)
- ⏸️ **Événements typing** - Logs console uniquement

### **Prochaines Étapes**
1. **Créer les routes backend** listées ci-dessus
2. **Tester les routes** avec Postman/Insomnia
3. **Réactiver les fonctions** côté frontend
4. **Tester l'ensemble** du système

## 🧪 **TEST DES ROUTES**

### **Test Manual avec cURL**
```bash
# Test route typing conversation
curl -X POST http://127.0.0.1:8001/api/messages/typing \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"conversation_id": 1, "is_typing": true}'

# Test route typing litige
curl -X POST http://127.0.0.1:8001/api/disputes/typing \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"dispute_id": 1, "is_typing": true}'
```

### **Réponse Attendue**
```json
{
  "success": true
}
```

---

## 📝 **NOTES**

- Les erreurs 405 sont **normales** en développement
- Le frontend est **configuré pour gérer** ces erreurs gracieusement
- Les **indicateurs visuels** de frappe fonctionnent toujours côté UI
- Seul l'**envoi au backend** est temporairement désactivé

Une fois le backend configuré, le système WebSocket sera **100% fonctionnel** ! 🚀
