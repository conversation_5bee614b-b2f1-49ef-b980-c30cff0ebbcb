/* Styles pour le dropdown des catégories */

.category-dropdown {
  position: relative;
}

.category-dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-width: 1200px;
  background-color: white;
  border-radius: 0 0 0.5rem 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  z-index: 50;
}

.category-dropdown:hover .category-dropdown-content {
  opacity: 1;
  visibility: visible;
}

.subcategory-image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.subcategory-item:hover .subcategory-image {
  transform: scale(1.05);
}

.subcategory-name {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
}

/* Styles pour le mode sombre */
.dark .category-dropdown-content {
  background-color: hsl(240 10% 3.9%);
  border-color: hsl(240 3.7% 15.9%);
}

/* Animation d'entrée pour le dropdown */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.category-dropdown:hover .category-dropdown-content {
  animation: fadeIn 0.2s ease forwards;
}
