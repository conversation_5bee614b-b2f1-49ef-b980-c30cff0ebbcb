import { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { User, Phone, Calendar, MapPin, CheckCircle } from 'lucide-react';
import EcommerceAuthLayout from '@/layouts/auth/ecommerce-auth-layout';
import InputError from '@/components/input-error';
import { useTranslation } from '@/hooks/use-translation';

export default function CompleteProfile() {
    const { translate } = useTranslation();
    const { data, setData, post, processing, errors } = useForm({
        prenom: '',
        nom: '',
        telephone: '',
        dateDeNaissance: '',
        rue: '',
        ville: '',
        etat: '',
        pays: 'Cameroun',
        codePostal: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('profile.complete'));
    };

    return (
        <EcommerceAuthLayout
            title={translate('pages.complete_profile.complete_profile')}
            description={translate('pages.complete_profile.description')}
        >
            <Head title={translate('pages.complete_profile.title')} />

            <div className="space-y-6">
                {/* Message d'information */}
                <Alert className="border-primary/20 bg-primary/5">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    <AlertDescription className="text-primary">
                        {translate('pages.complete_profile.success_message')}
                    </AlertDescription>
                </Alert>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Informations personnelles */}
                    <div className="space-y-4">
                        <div className="flex items-center space-x-2 mb-4">
                            <User className="h-5 w-5 text-primary" />
                            <h3 className="text-lg font-semibold">{translate('pages.complete_profile.personal_info')}</h3>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="prenom">{translate('pages.complete_profile.first_name')} *</Label>
                                <Input
                                    id="prenom"
                                    type="text"
                                    required
                                    value={data.prenom}
                                    onChange={(e) => setData('prenom', e.target.value)}
                                    placeholder={translate('pages.complete_profile.first_name')}
                                />
                                <InputError message={errors.prenom} />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="nom">{translate('pages.complete_profile.last_name')} *</Label>
                                <Input
                                    id="nom"
                                    type="text"
                                    required
                                    value={data.nom}
                                    onChange={(e) => setData('nom', e.target.value)}
                                    placeholder={translate('pages.complete_profile.last_name')}
                                />
                                <InputError message={errors.nom} />
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="telephone">
                                    <Phone className="inline h-4 w-4 mr-1" />
                                    {translate('pages.complete_profile.phone')}
                                </Label>
                                <Input
                                    id="telephone"
                                    type="tel"
                                    value={data.telephone}
                                    onChange={(e) => setData('telephone', e.target.value)}
                                    placeholder="+237 6 12 34 56 78"
                                />
                                <InputError message={errors.telephone} />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="dateDeNaissance">
                                    <Calendar className="inline h-4 w-4 mr-1" />
                                    {translate('pages.complete_profile.birth_date')}
                                </Label>
                                <Input
                                    id="dateDeNaissance"
                                    type="date"
                                    value={data.dateDeNaissance}
                                    onChange={(e) => setData('dateDeNaissance', e.target.value)}
                                />
                                <InputError message={errors.dateDeNaissance} />
                            </div>
                        </div>
                    </div>

                    {/* Adresse */}
                    <div className="space-y-4">
                        <div className="flex items-center space-x-2 mb-4">
                            <MapPin className="h-5 w-5 text-primary" />
                            <h3 className="text-lg font-semibold">{translate('pages.complete_profile.delivery_address')}</h3>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="rue">{translate('pages.complete_profile.address')} *</Label>
                            <Input
                                id="rue"
                                type="text"
                                required
                                value={data.rue}
                                onChange={(e) => setData('rue', e.target.value)}
                                placeholder="123 Rue de la Paix"
                            />
                            <InputError message={errors.rue} />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="ville">{translate('pages.complete_profile.city')} *</Label>
                                <Input
                                    id="ville"
                                    type="text"
                                    required
                                    value={data.ville}
                                    onChange={(e) => setData('ville', e.target.value)}
                                    placeholder="Yaoundé"
                                />
                                <InputError message={errors.ville} />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="codePostal">{translate('pages.complete_profile.postal_code')} *</Label>
                                <Input
                                    id="codePostal"
                                    type="text"
                                    required
                                    value={data.codePostal}
                                    onChange={(e) => setData('codePostal', e.target.value)}
                                    placeholder="00237"
                                />
                                <InputError message={errors.codePostal} />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="etat">{translate('pages.complete_profile.state')}</Label>
                                <Input
                                    id="etat"
                                    type="text"
                                    value={data.etat}
                                    onChange={(e) => setData('etat', e.target.value)}
                                    placeholder="Centre"
                                />
                                <InputError message={errors.etat} />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="pays">{translate('pages.complete_profile.country')} *</Label>
                            <Input
                                id="pays"
                                type="text"
                                required
                                value={data.pays}
                                onChange={(e) => setData('pays', e.target.value)}
                                placeholder="Cameroun"
                            />
                            <InputError message={errors.pays} />
                        </div>
                    </div>

                    {/* Bouton de soumission */}
                    <Button
                        type="submit"
                        className="w-full"
                        disabled={processing}
                    >
                        {processing ? translate('pages.complete_profile.saving') : translate('pages.complete_profile.finalize_profile')}
                    </Button>
                </form>

                {/* Note */}
                <div className="text-center text-sm text-muted-foreground">
                    <p>{translate('pages.complete_profile.required_fields')}</p>
                    <p className="mt-1">
                        {translate('pages.complete_profile.info_note')}
                    </p>
                </div>
            </div>
        </EcommerceAuthLayout>
    );
}
