// Components
import { Head, useForm, Link } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import EcommerceAuthLayout from '@/layouts/auth/ecommerce-auth-layout';
import { useTranslation } from '@/hooks/use-translation';

export default function ForgotPassword({ status }: { status?: string }) {
    const { translate } = useTranslation();
    const { data, setData, post, processing, errors } = useForm<Required<{ email: string }>>({
        email: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('password.email'));
    };

    return (
        <EcommerceAuthLayout
            title={translate('pages.forgot_password.form_title')}
            description={translate('pages.forgot_password.description')}
        >
            <Head title={translate('pages.forgot_password.title')} />

            {status && <div className="mb-4 text-center text-sm font-medium text-green-600">{status}</div>}

            <div className="space-y-6">
                <form onSubmit={submit}>
                    <div className="grid gap-2">
                        <Label htmlFor="email">{translate('pages.forgot_password.email')}</Label>
                        <Input
                            id="email"
                            type="email"
                            name="email"
                            autoComplete="off"
                            value={data.email}
                            autoFocus
                            onChange={(e) => setData('email', e.target.value)}
                            placeholder={translate('pages.forgot_password.email_placeholder')}
                        />

                        <InputError message={errors.email} />
                    </div>

                    <div className="my-6 flex items-center justify-start">
                        <Button className="w-full" disabled={processing}>
                            {processing && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
                            {processing ? translate('pages.forgot_password.sending') : translate('pages.forgot_password.send_reset_link')}
                        </Button>
                    </div>
                </form>

                <div className="text-muted-foreground space-x-1 text-center text-sm">
                    <span>{translate('pages.forgot_password.back_to_login')}</span>
                    <Link href={route('login')} className="text-primary hover:underline">
                        {translate('pages.forgot_password.login_link')}
                    </Link>
                </div>
            </div>
        </EcommerceAuthLayout>
    );
}
