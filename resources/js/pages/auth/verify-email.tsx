// Components
import { <PERSON>, useForm, <PERSON> } from '@inertiajs/react';
import { LoaderCircle, Mail, CheckCircle, RefreshCw, ArrowLeft } from 'lucide-react';
import { FormEventHandler } from 'react';

import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import EcommerceAuthLayout from '@/layouts/auth/ecommerce-auth-layout';
import { useTranslation } from '@/hooks/use-translation';

export default function VerifyEmail({ status }: { status?: string }) {
    const { post, processing } = useForm({});
    const { translate } = useTranslation();

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('verification.send'));
    };

    return (
        <EcommerceAuthLayout
            title={translate('pages.verify_email.verify_email')}
            description={translate('pages.verify_email.verification_sent')}
        >
            <Head title={translate('pages.verify_email.title')} />

            <div className="space-y-6">
                {/* Ic<PERSON> et titre principal */}
                <div className="text-center">
                    <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 mb-4">
                        <Mail className="h-8 w-8 text-primary" />
                    </div>
                    <h2 className="text-xl font-semibold mb-2">
                        {translate('pages.verify_email.verify_email')}
                    </h2>
                    <p className="text-sm text-muted-foreground">
                        {translate('pages.verify_email.verification_sent')}
                    </p>
                </div>

                {/* Message de statut */}
                {status === 'verification-link-sent' && (
                    <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
                        <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                        <AlertDescription className="text-green-800 dark:text-green-200">
                            {translate('pages.verify_email.verification_link_sent')}
                        </AlertDescription>
                    </Alert>
                )}

                {/* Instructions */}
                <div className="bg-muted/50 rounded-lg p-4 text-center">
                    <p className="text-sm text-muted-foreground mb-2">
                        {translate('pages.verify_email.access_dashboard')}
                    </p>
                    <p className="text-sm text-muted-foreground">
                        {translate('pages.verify_email.click_link')}
                    </p>
                </div>

                {/* Actions */}
                <form onSubmit={submit} className="space-y-4">
                    <Button
                        disabled={processing}
                        className="w-full"
                        type="submit"
                    >
                        {processing ? (
                            <>
                                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                {translate('pages.verify_email.sending')}
                            </>
                        ) : (
                            <>
                                <Mail className="mr-2 h-4 w-4" />
                                {translate('pages.verify_email.resend_verification')}
                            </>
                        )}
                    </Button>

                    <Link
                        href={route('logout')}
                        method="post"
                        as="button"
                        className="w-full inline-flex items-center justify-center px-4 py-2 border border-border rounded-md shadow-sm text-sm font-medium text-muted-foreground bg-background hover:bg-muted focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                    >
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        {translate('pages.verify_email.logout')}
                    </Link>
                </form>

                {/* Aide */}
                <div className="border-t border-border pt-6">
                    <div className="text-center">
                        <h4 className="text-sm font-medium mb-2">
                            {translate('pages.verify_email.not_receiving')}
                        </h4>
                        <div className="text-xs text-muted-foreground space-y-1">
                            <p>{translate('pages.verify_email.check_spam')}</p>
                            <p>{translate('pages.verify_email.check_email')}</p>
                            <p>{translate('pages.verify_email.wait_minutes')}</p>
                        </div>
                    </div>
                </div>

                {/* Informations sur la vérification */}
                <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                    <div className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mt-0.5 mr-3 flex-shrink-0" />
                        <div className="text-sm">
                            <p className="font-medium text-primary mb-1">
                                {translate('pages.verify_email.why_verify')}
                            </p>
                            <ul className="text-muted-foreground space-y-1">
                                <li>{translate('pages.verify_email.secure_account')}</li>
                                <li>{translate('pages.verify_email.receive_notifications')}</li>
                                <li>{translate('pages.verify_email.recover_password')}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </EcommerceAuthLayout>
    );
}
