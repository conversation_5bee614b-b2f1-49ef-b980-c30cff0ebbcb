// import { useState } from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import ClientDashboardLayout from '@/layouts/client-dashboard-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    ShoppingBag,
    Package,
    Clock,
    CheckCircle,
    TrendingUp,
    Eye,
    ArrowRight,
    User,
    MapPin
} from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';

interface DashboardProps {
    client: any;
    stats: {
        total_orders: number;
        total_spent: number;
        pending_orders: number;
        delivered_orders: number;
    };
    recentOrders: any[];
    activeOrders: any[];
}

export default function Dashboard({ client, stats, recentOrders, activeOrders }: DashboardProps) {
    const { translate } = useTranslation();

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('fr-FR', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(price);
    };

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            'EnAttente': { label: translate('pages.orders.pending'), variant: 'secondary' as const },
            'EnCoursDeTraitement': { label: translate('pages.orders.processing'), variant: 'default' as const },
            'Expedie': { label: translate('pages.orders.shipped'), variant: 'default' as const },
            'Livre': { label: translate('pages.orders.delivered'), variant: 'default' as const },
            'Annule': { label: translate('pages.orders.cancelled'), variant: 'destructive' as const },
        };

        const config = statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'secondary' as const };
        return <Badge variant={config.variant}>{config.label}</Badge>;
    };

    return (
        <ClientDashboardLayout 
            title={`${translate('pages.dashboard.welcome')}, ${client.nom} ${client.prenom} 👋`}
            description={translate('pages.dashboard.overview')}
        >
            <Head title={translate('pages.dashboard.title')} />

            {/* Statistiques */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">{translate('pages.dashboard.total_orders')}</CardTitle>
                        <ShoppingBag className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.total_orders}</div>
                        <p className="text-xs text-muted-foreground">
                            {translate('pages.dashboard.since_registration')}
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">{translate('pages.dashboard.total_spent')}</CardTitle>
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{formatPrice(stats.total_spent)} FCFA</div>
                        <p className="text-xs text-muted-foreground">
                            {translate('pages.dashboard.total_purchases')}
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">{translate('pages.dashboard.pending_orders')}</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.pending_orders}</div>
                        <p className="text-xs text-muted-foreground">
                            {translate('pages.dashboard.orders_in_progress')}
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">{translate('pages.dashboard.delivered_orders')}</CardTitle>
                        <CheckCircle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.delivered_orders}</div>
                        <p className="text-xs text-muted-foreground">
                            {translate('pages.dashboard.completed_orders')}
                        </p>
                    </CardContent>
                </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Commandes en cours */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="flex items-center gap-2">
                                    <Package className="h-5 w-5" />
                                    {translate('pages.dashboard.active_orders')}
                                </CardTitle>
                                <CardDescription>
                                    {translate('pages.dashboard.track_active_orders')}
                                </CardDescription>
                            </div>
                            <Link href={route('dashboard.orders')}>
                                <Button variant="outline" size="sm" className="cursor-pointer">
                                    {translate('pages.dashboard.view_all')}
                                    <ArrowRight className="h-4 w-4 ml-2" />
                                </Button>
                            </Link>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {activeOrders.length > 0 ? (
                            <div className="space-y-4">
                                {activeOrders.slice(0, 3).map((order) => (
                                    <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="font-medium">#{order.numero_commande}</span>
                                                {getStatusBadge(order.statut_global)}
                                            </div>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                {translate('pages.dashboard.merchant_count', { count: order.sous_commandes.length })} • {formatPrice(order.montant_total_ttc)} FCFA
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {new Date(order.created_at).toLocaleDateString('fr-FR')}
                                            </p>
                                        </div>
                                        <Link href={route('dashboard.order-details', order.numero_commande)}>
                                            <Button variant="ghost" size="sm" className="cursor-pointer">
                                                <Eye className="h-4 w-4" />
                                            </Button>
                                        </Link>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                <p className="text-gray-500">{translate('pages.dashboard.no_active_orders')}</p>
                                <Link href={route('home')}>
                                    <Button className="mt-4 cursor-pointer">
                                        {translate('pages.dashboard.start_shopping')}
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Commandes récentes */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="flex items-center gap-2">
                                    <Clock className="h-5 w-5" />
                                    {translate('pages.dashboard.recent_orders')}
                                </CardTitle>
                                <CardDescription>
                                    {translate('pages.dashboard.recent_orders_desc')}
                                </CardDescription>
                            </div>
                            <Link href={route('dashboard.orders')}>
                                <Button variant="outline" size="sm" className="cursor-pointer">
                                    {translate('pages.dashboard.history')}
                                    <ArrowRight className="h-4 w-4 ml-2" />
                                </Button>
                            </Link>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {recentOrders.length > 0 ? (
                            <div className="space-y-4">
                                {recentOrders.slice(0, 3).map((order) => (
                                    <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="font-medium">#{order.numero_commande}</span>
                                                {getStatusBadge(order.statut_global)}
                                            </div>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                {translate('pages.dashboard.merchant_count', { count: order.sous_commandes.length })} • {formatPrice(order.montant_total_ttc)} FCFA
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {new Date(order.created_at).toLocaleDateString('fr-FR')}
                                            </p>
                                        </div>
                                        <Link href={route('dashboard.order-details', order.numero_commande)}>
                                            <Button variant="ghost" size="sm" className="cursor-pointer">
                                                <Eye className="h-4 w-4" />
                                            </Button>
                                        </Link>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <ShoppingBag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                <p className="text-gray-500">{translate('pages.dashboard.no_recent_orders')}</p>
                                <Link href={route('home')}>
                                    <Button className="mt-4 cursor-pointer">
                                        {translate('pages.dashboard.discover_products')}
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Actions rapides */}
            <div className="mt-8">
                <Card>
                    <CardHeader>
                        <CardTitle>{translate('pages.dashboard.quick_actions')}</CardTitle>
                        <CardDescription>
                            {translate('pages.dashboard.quick_actions_desc')}
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Link href={route('dashboard.orders')}>
                                <Button variant="outline" className="w-full justify-start cursor-pointer">
                                    <ShoppingBag className="h-4 w-4 mr-2" />
                                    {translate('pages.dashboard.my_orders')}
                                </Button>
                            </Link>
                            <Link href={route('dashboard.profile')}>
                                <Button variant="outline" className="w-full justify-start cursor-pointer">
                                    <User className="h-4 w-4 mr-2" />
                                    {translate('pages.dashboard.my_profile')}
                                </Button>
                            </Link>
                            <Link href={route('my-account')}>
                                <Button variant="outline" className="w-full justify-start cursor-pointer">
                                    <MapPin className="h-4 w-4 mr-2" />
                                    {translate('pages.dashboard.my_addresses')}
                                </Button>
                            </Link>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </ClientDashboardLayout>
    );
}
