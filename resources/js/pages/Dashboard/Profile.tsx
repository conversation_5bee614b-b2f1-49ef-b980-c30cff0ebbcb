import { Head, <PERSON> } from '@inertiajs/react';
import ClientDashboardLayout from '@/layouts/client-dashboard-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    User,
    Mail,
    Phone,
    Calendar,
    MapPin,
    Edit,
    Settings,
    Shield
} from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import UserAvatarUpload from '@/components/profile/UserAvatarUpload';

interface ProfileProps {
    client: {
        id: string;
        nom: string;
        prenom: string;
        telephone?: string;
        dateDeNaissance?: string;
        created_at: string;
        adresses: Array<{
            id: number;
            rue: string;
            ville: string;
            etat: string;
            pays: string;
            codePostal: string;
            type: string;
        }>;
    };
    user: {
        id: number;
        name: string;
        email: string;
        email_verified_at?: string;
        created_at: string;
    };
}

export default function Profile({ client, user }: ProfileProps) {
    const { translate } = useTranslation();

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('fr-FR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    return (
        <ClientDashboardLayout 
            title={translate('pages.dashboard_profile.my_profile')}
            description={translate('pages.dashboard_profile.manage_profile')}
        >
            <Head title={translate('pages.dashboard_profile.title')} />

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Colonne principale */}
                <div className="lg:col-span-2 space-y-6">
                    {/* Informations personnelles */}
                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        {translate('pages.dashboard_profile.personal_info')}
                                    </CardTitle>
                                    <CardDescription>
                                        {translate('pages.dashboard_profile.basic_info')}
                                    </CardDescription>
                                </div>
                                <Link href={route('my-account')}>
                                    <Button variant="outline" size="sm">
                                        <Edit className="h-4 w-4 mr-2" />
                                        {translate('pages.dashboard_profile.edit')}
                                    </Button>
                                </Link>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                        {translate('pages.dashboard_profile.first_name')}
                                    </label>
                                    <p className="text-lg font-medium">{client.prenom}</p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                        {translate('pages.dashboard_profile.last_name')}
                                    </label>
                                    <p className="text-lg font-medium">{client.nom}</p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                        {translate('pages.dashboard_profile.email')}
                                    </label>
                                    <p className="text-lg font-medium flex items-center gap-2">
                                        {user.email}
                                        {user.email_verified_at && (
                                            <span className="text-green-600 text-sm">✓ {translate('pages.dashboard_profile.yes')}</span>
                                        )}
                                    </p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                        {translate('pages.dashboard_profile.phone')}
                                    </label>
                                    <p className="text-lg font-medium">
                                        {client.telephone || translate('pages.dashboard_profile.no')}
                                    </p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                        {translate('pages.dashboard_profile.birth_date')}
                                    </label>
                                    <p className="text-lg font-medium">
                                        {client.dateDeNaissance ? formatDate(client.dateDeNaissance) : translate('pages.dashboard_profile.no')}
                                    </p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                        {translate('pages.dashboard_profile.member_since')}
                                    </label>
                                    <p className="text-lg font-medium">{formatDate(client.created_at)}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Adresses */}
                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="flex items-center gap-2">
                                        <MapPin className="h-5 w-5" />
                                        {translate('pages.dashboard_profile.my_addresses')}
                                    </CardTitle>
                                    <CardDescription>
                                        {translate('pages.dashboard_profile.manage_addresses')}
                                    </CardDescription>
                                </div>
                                <Link href={route('my-account')}>
                                    <Button variant="outline" size="sm">
                                        <Edit className="h-4 w-4 mr-2" />
                                        {translate('pages.dashboard_profile.edit')}
                                    </Button>
                                </Link>
                            </div>
                        </CardHeader>
                        <CardContent>
                            {client.adresses.length > 0 ? (
                                <div className="space-y-4">
                                    {client.adresses.slice(0, 3).map((address) => (
                                        <div key={address.id} className="p-4 border rounded-lg">
                                            <div className="flex items-start justify-between">
                                                <div>
                                                    <p className="font-medium">{address.type}</p>
                                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                                        {address.rue}
                                                    </p>
                                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                                        {address.ville}, {address.etat} {address.codePostal}
                                                    </p>
                                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                                        {address.pays}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                    {client.adresses.length > 3 && (
                                        <p className="text-sm text-gray-500 text-center">
                                            {translate('pages.dashboard_profile.more_addresses', { count: client.adresses.length - 3 })}
                                        </p>
                                    )}
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <p className="text-gray-500 mb-4">{translate('pages.dashboard_profile.no_addresses')}</p>
                                    <p className="text-sm text-gray-400 mb-4">{translate('pages.dashboard_profile.add_first_address')}</p>
                                    <Link href={route('my-account')}>
                                        <Button>
                                            {translate('pages.dashboard_profile.add_address')}
                                        </Button>
                                    </Link>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Colonne latérale */}
                <div className="space-y-6">
                    {/* Photo de profil */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Photo de profil</CardTitle>
                            <CardDescription>
                                Gérez votre photo de profil
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="flex justify-center">
                            <UserAvatarUpload size="xl" />
                        </CardContent>
                    </Card>

                    {/* Actions rapides */}
                    <Card>
                        <CardHeader>
                            <CardTitle>{translate('pages.dashboard_profile.quick_actions')}</CardTitle>
                            <CardDescription>
                                {translate('pages.dashboard_profile.quick_actions')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <Link href={route('my-account')}>
                                <Button variant="outline" className="w-full justify-start">
                                    <User className="h-4 w-4 mr-2" />
                                    {translate('pages.dashboard_profile.edit_profile')}
                                </Button>
                            </Link>
                            <Link href={route('password.edit')}>
                                <Button variant="outline" className="w-full justify-start">
                                    <Shield className="h-4 w-4 mr-2" />
                                    {translate('pages.dashboard_profile.change_password')}
                                </Button>
                            </Link>
                            <Link href={route('appearance')}>
                                <Button variant="outline" className="w-full justify-start">
                                    <Settings className="h-4 w-4 mr-2" />
                                    {translate('pages.appearance.preferences')}
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>

                    {/* Statistiques du compte */}
                    <Card>
                        <CardHeader>
                            <CardTitle>{translate('pages.dashboard_profile.account_stats')}</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <div className="flex justify-between">
                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                    {translate('pages.dashboard_profile.registered_addresses')}
                                </span>
                                <span className="font-medium">{client.adresses.length}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                    {translate('pages.dashboard_profile.email_verified')}
                                </span>
                                <span className="font-medium">
                                    {user.email_verified_at ? translate('pages.dashboard_profile.yes') : translate('pages.dashboard_profile.no')}
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                    {translate('pages.dashboard_profile.account_created')}
                                </span>
                                <span className="font-medium">
                                    {new Date(client.created_at).getFullYear()}
                                </span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Sécurité */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Shield className="h-5 w-5" />
                                {translate('pages.dashboard_profile.security')}
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="font-medium">{translate('pages.dashboard_profile.password_security')}</p>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {translate('pages.dashboard_profile.recent_modification')}
                                    </p>
                                </div>
                                <Link href={route('password.edit')}>
                                    <Button variant="outline" size="sm">
                                        {translate('pages.dashboard_profile.edit')}
                                    </Button>
                                </Link>
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="font-medium">{translate('pages.dashboard_profile.email')}</p>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {user.email_verified_at ? translate('pages.dashboard_profile.yes') : translate('pages.dashboard_profile.no')}
                                    </p>
                                </div>
                                {!user.email_verified_at && (
                                    <Button variant="outline" size="sm">
                                        Vérifier
                                    </Button>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </ClientDashboardLayout>
    );
}
