import { Transition } from '@headlessui/react';
import { Head, Link, useForm } from '@inertiajs/react';
import { FormEventHandler, useRef } from 'react';
import { ArrowLeft, Shield, Eye, EyeOff } from 'lucide-react';
import { useState } from 'react';

import EcommerceLayout from '@/layouts/ecommerce-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslation } from '@/hooks/use-translation';

export default function Password() {
    const { translate } = useTranslation();
    const passwordInput = useRef<HTMLInputElement>(null);
    const currentPasswordInput = useRef<HTMLInputElement>(null);

    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const { data, setData, errors, put, reset, processing, recentlySuccessful } = useForm({
        current_password: '',
        password: '',
        password_confirmation: '',
    });

    const updatePassword: FormEventHandler = (e) => {
        e.preventDefault();

        put(route('password.update'), {
            preserveScroll: true,
            onSuccess: () => reset(),
            onError: (errors) => {
                if (errors.password) {
                    reset('password', 'password_confirmation');
                    passwordInput.current?.focus();
                }

                if (errors.current_password) {
                    reset('current_password');
                    currentPasswordInput.current?.focus();
                }
            },
        });
    };

    return (
        <EcommerceLayout>
            <Head title={translate('pages.password.title')} />

            <div className="container mx-auto px-4 py-8">
                {/* En-tête */}
                <div className="mb-8">
                    <div className="flex items-center gap-4 mb-4">
                        <Link href={route('dashboard')}>
                            <Button variant="ghost" size="sm" className="cursor-pointer">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                {translate('pages.dashboard.back_to_dashboard')}
                            </Button>
                        </Link>
                    </div>
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                        {translate('pages.password.change_password')}
                    </h1>
                    <p className="text-gray-600 dark:text-gray-400 mt-2">
                        {translate('pages.password.description')}
                    </p>
                </div>

                <div className="max-w-2xl">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Shield className="h-5 w-5" />
                                {translate('pages.password.security')}
                            </CardTitle>
                            <CardDescription>
                                {translate('pages.password.security_desc')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={updatePassword} className="space-y-6">
                                {/* Mot de passe actuel */}
                                <div className="space-y-2">
                                    <Label htmlFor="current_password">
                                        {translate('pages.password.current_password')}
                                    </Label>
                                    <div className="relative">
                                        <Input
                                            id="current_password"
                                            ref={currentPasswordInput}
                                            value={data.current_password}
                                            onChange={(e) => setData('current_password', e.target.value)}
                                            type={showCurrentPassword ? "text" : "password"}
                                            className="pr-10"
                                            autoComplete="current-password"
                                            placeholder={translate('pages.password.current_password_placeholder')}
                                        />
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                        >
                                            {showCurrentPassword ? (
                                                <EyeOff className="h-4 w-4" />
                                            ) : (
                                                <Eye className="h-4 w-4" />
                                            )}
                                        </Button>
                                    </div>
                                    {errors.current_password && (
                                        <p className="text-sm text-red-600">{errors.current_password}</p>
                                    )}
                                </div>

                                {/* Nouveau mot de passe */}
                                <div className="space-y-2">
                                    <Label htmlFor="password">
                                        {translate('pages.password.new_password')}
                                    </Label>
                                    <div className="relative">
                                        <Input
                                            id="password"
                                            ref={passwordInput}
                                            value={data.password}
                                            onChange={(e) => setData('password', e.target.value)}
                                            type={showNewPassword ? "text" : "password"}
                                            className="pr-10"
                                            autoComplete="new-password"
                                            placeholder={translate('pages.password.new_password_placeholder')}
                                        />
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                            onClick={() => setShowNewPassword(!showNewPassword)}
                                        >
                                            {showNewPassword ? (
                                                <EyeOff className="h-4 w-4" />
                                            ) : (
                                                <Eye className="h-4 w-4" />
                                            )}
                                        </Button>
                                    </div>
                                    {errors.password && (
                                        <p className="text-sm text-red-600">{errors.password}</p>
                                    )}
                                </div>

                                {/* Confirmation mot de passe */}
                                <div className="space-y-2">
                                    <Label htmlFor="password_confirmation">
                                        {translate('pages.password.confirm_password')}
                                    </Label>
                                    <div className="relative">
                                        <Input
                                            id="password_confirmation"
                                            value={data.password_confirmation}
                                            onChange={(e) => setData('password_confirmation', e.target.value)}
                                            type={showConfirmPassword ? "text" : "password"}
                                            className="pr-10"
                                            autoComplete="new-password"
                                            placeholder={translate('pages.password.confirm_password_placeholder')}
                                        />
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                        >
                                            {showConfirmPassword ? (
                                                <EyeOff className="h-4 w-4" />
                                            ) : (
                                                <Eye className="h-4 w-4" />
                                            )}
                                        </Button>
                                    </div>
                                    {errors.password_confirmation && (
                                        <p className="text-sm text-red-600">{errors.password_confirmation}</p>
                                    )}
                                </div>

                                {/* Boutons */}
                                <div className="flex items-center gap-4 pt-4">
                                    <Button disabled={processing} className="cursor-pointer">
                                        {processing ? translate('pages.password.saving') : translate('pages.password.save_password')}
                                    </Button>

                                    <Transition
                                        show={recentlySuccessful}
                                        enter="transition ease-in-out"
                                        enterFrom="opacity-0"
                                        leave="transition ease-in-out"
                                        leaveTo="opacity-0"
                                    >
                                        <p className="text-sm text-green-600">{translate('pages.password.saved')}</p>
                                    </Transition>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </EcommerceLayout>
    );
}
