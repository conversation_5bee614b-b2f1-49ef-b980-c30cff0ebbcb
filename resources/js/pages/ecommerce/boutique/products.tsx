import { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import { marchandService } from '@/services/MarchandService';
import { productService } from '@/services/ProductService';
import { Marchand } from '@/models/Marchand';
import { Product } from '@/models/Product';
import BoutiqueHeader from '@/components/ecommerce/boutique/BoutiqueHeader';
import CardProduit from '@/components/ecommerce/CardProduit';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { AlertCircle, Search, Filter, Grid, List } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';

interface BoutiqueProductsProps {
  marchandSlug: string;
}

export default function BoutiqueProducts({ marchandSlug }: BoutiqueProductsProps) {
  const { translate } = useTranslation();
  const [marchand, setMarchand] = useState<Marchand | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [categoryFilter, setCategoryFilter] = useState('');

  useEffect(() => {
    loadBoutiqueData();
  }, [marchandSlug]);

  useEffect(() => {
    if (marchand) {
      loadProducts();
    }
  }, [marchand, currentPage, sortBy, sortOrder, searchQuery, categoryFilter]);

  const loadBoutiqueData = async () => {
    try {
      setLoading(true);
      setError(null);

      const marchandData = await marchandService.getMarchandBySlug(marchandSlug);

      if (!marchandData) {
        setError(translate('pages.boutique.not_found'));
        return;
      }

      setMarchand(marchandData);

      const statsData = await marchandService.getMarchandStats(marchandData.id);
      setStats(statsData);

    } catch (err) {
      console.error('Erreur lors du chargement de la boutique:', err);
      setError(translate('pages.boutique.error'));
    } finally {
      setLoading(false);
    }
  };

  const loadProducts = async () => {
    if (!marchand) return;

    try {
      const productsData = await productService.getProductsByMarchand(
        marchand.id.toString(),
        currentPage,
        12, // 12 produits par page
        categoryFilter || undefined,
        sortBy,
        sortOrder,
        searchQuery || undefined
      );

      setProducts(productsData.products || []);
      setTotalPages(productsData.pagination?.totalPages || 1);
      setTotalItems(productsData.pagination?.totalItems || 0);

    } catch (err) {
      console.error('Erreur lors du chargement des produits:', err);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
  };

  const handleSortChange = (value: string) => {
    const [field, order] = value.split('-');
    setSortBy(field);
    setSortOrder(order);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  if (loading) {
    return (
      <EcommerceLayout>
        <Head title={translate('pages.boutique.loading')} />
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <div className="space-y-6">
            <Skeleton className="h-48 w-full" />
            <div className="container mx-auto px-4 py-8">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {Array.from({ length: 12 }).map((_, index) => (
                  <div key={index} className="space-y-3">
                    <Skeleton className="h-48 w-full rounded-lg" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </EcommerceLayout>
    );
  }

  if (error || !marchand) {
    return (
      <EcommerceLayout>
        <Head title={translate('pages.boutique.not_found')} />
        <div className="container mx-auto px-4 py-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error || translate('pages.boutique.not_found')}
            </AlertDescription>
          </Alert>
        </div>
      </EcommerceLayout>
    );
  }

  return (
    <EcommerceLayout>
      <Head
        title={`${translate('common.shop_products')} - ${marchand.nomEntreprise}`}
        description={`Découvrez tous les produits de ${marchand.nomEntreprise}`}
      />

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <BoutiqueHeader marchand={marchand} stats={stats} />

        <div className="container mx-auto px-4 py-8">
          {/* Filtres et recherche */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              {/* Recherche */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder={translate('common.search_products')}
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Tri et vue */}
              <div className="flex items-center space-x-4">
                <Select value={`${sortBy}-${sortOrder}`} onValueChange={handleSortChange}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="created_at-desc">{translate('common.sort_newest')}</SelectItem>
                    <SelectItem value="created_at-asc">{translate('common.sort_oldest')}</SelectItem>
                    <SelectItem value="nom-asc">{translate('common.sort_name_asc')}</SelectItem>
                    <SelectItem value="nom-desc">{translate('common.sort_name_desc')}</SelectItem>
                    <SelectItem value="prix-asc">{translate('common.sort_price_asc')}</SelectItem>
                    <SelectItem value="prix-desc">{translate('common.sort_price_desc')}</SelectItem>
                  </SelectContent>
                </Select>

                <div className="flex border rounded-lg">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Résultats */}
            <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
              {totalItems} {translate('common.products_found')}
            </div>
          </div>

          {/* Produits */}
          {products.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 dark:text-gray-600 mb-4">
                <Search className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                {translate('pages.boutique.no_products')}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {searchQuery
                  ? translate('common.no_search_results')
                  : translate('pages.boutique.no_products_description')
                }
              </p>
            </div>
          ) : (
            <>
              <div className={`grid gap-6 ${
                viewMode === 'grid'
                  ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                  : 'grid-cols-1'
              }`}>
                {products.map((product) => (
                  <CardProduit
                    key={product.id}
                    product={product}
                    layout={viewMode}
                  />
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-12 flex justify-center">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      disabled={currentPage === 1}
                      onClick={() => handlePageChange(currentPage - 1)}
                    >
                      {translate('common.previous')}
                    </Button>

                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1;
                      return (
                        <Button
                          key={page}
                          variant={currentPage === page ? 'default' : 'outline'}
                          onClick={() => handlePageChange(page)}
                        >
                          {page}
                        </Button>
                      );
                    })}

                    <Button
                      variant="outline"
                      disabled={currentPage === totalPages}
                      onClick={() => handlePageChange(currentPage + 1)}
                    >
                      {translate('common.next')}
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </EcommerceLayout>
  );
}
