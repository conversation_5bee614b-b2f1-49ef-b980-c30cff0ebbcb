import { useState } from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import ClientDashboardLayout from '@/layouts/client-dashboard-layout';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Input } from '@/Components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { 
    MessageSquare, 
    Plus, 
    Search, 
    Filter,
    Clock,
    CheckCircle,
    User,
    Package,
    Calendar,
    MessageCircle,
    Store
} from 'lucide-react';

interface Conversation {
    id: string;
    sujet: string;
    statut: string;
    statut_formate: string;
    statut_color: string;
    type_conversation: string;
    type_formate: string;
    priorite_formate: string;
    priorite_color: string;
    date_creation: string;
    date_dernier_message?: string;
    nombre_messages_total: number;
    messages_non_lus_client: number;
    marchand: {
        nom_entreprise: string;
        logo?: string;
    };
    commande_principale?: {
        numero_commande: string;
    };
    produit?: {
        nom: string;
        image_principale?: string;
    };
    messages: Array<{
        id: string;
        message: string;
        auteur_type: string;
        created_at: string;
    }>;
}

interface PaginationMeta {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Props {
    conversations: {
        data: Conversation[];
        meta?: PaginationMeta;
        links?: any;
    };
    stats: {
        total_conversations: number;
        conversations_actives: number;
        messages_non_lus: number;
        conversations_recentes: number;
    };
    filters: {
        statut?: string;
        type?: string;
        marchand_id?: string;
    };
    typesConversation: Record<string, string>;
    statutsConversation: Record<string, string>;
}

export default function ConversationsIndex({ 
    conversations, 
    stats, 
    filters, 
    typesConversation, 
    statutsConversation 
}: Props) {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedStatut, setSelectedStatut] = useState(filters.statut || 'all');
    const [selectedType, setSelectedType] = useState(filters.type || 'all');

    const handleFilter = () => {
        router.get(route('client.conversations.index'), {
            statut: selectedStatut && selectedStatut !== 'all' ? selectedStatut : undefined,
            type: selectedType && selectedType !== 'all' ? selectedType : undefined,
            search: searchTerm || undefined,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedStatut('all');
        setSelectedType('all');
        router.get(route('client.conversations.index'));
    };

    const getStatusIcon = (statut: string) => {
        switch (statut) {
            case 'active':
                return <MessageCircle className="h-4 w-4" />;
            case 'fermee':
                return <CheckCircle className="h-4 w-4" />;
            case 'archivee':
                return <Clock className="h-4 w-4" />;
            default:
                return <MessageSquare className="h-4 w-4" />;
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <ClientDashboardLayout>
            <Head title="Mes Conversations" />

            <div className="space-y-6">
                {/* En-tête */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                            Mes Conversations
                        </h1>
                        <p className="text-gray-600 dark:text-gray-400">
                            Échangez directement avec les marchands
                        </p>
                    </div>
                    <Link href={route('client.conversations.create')}>
                        <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            Nouvelle Conversation
                        </Button>
                    </Link>
                </div>

                {/* Statistiques */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <MessageSquare className="h-5 w-5 text-blue-500" />
                                <div>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">Total</p>
                                    <p className="text-2xl font-bold">{stats.total_conversations}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <MessageCircle className="h-5 w-5 text-green-500" />
                                <div>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">Actives</p>
                                    <p className="text-2xl font-bold">{stats.conversations_actives}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <User className="h-5 w-5 text-orange-500" />
                                <div>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">Non lus</p>
                                    <p className="text-2xl font-bold">{stats.messages_non_lus}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <Clock className="h-5 w-5 text-purple-500" />
                                <div>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">Récentes</p>
                                    <p className="text-2xl font-bold">{stats.conversations_recentes}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filtres */}
                <Card>
                    <CardContent className="p-4">
                        <div className="flex flex-col md:flex-row gap-4">
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    <Input
                                        placeholder="Rechercher par sujet ou marchand..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                            </div>
                            
                            <Select value={selectedStatut} onValueChange={setSelectedStatut}>
                                <SelectTrigger className="w-full md:w-48">
                                    <SelectValue placeholder="Statut" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Tous les statuts</SelectItem>
                                    {Object.entries(statutsConversation).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>{label}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={selectedType} onValueChange={setSelectedType}>
                                <SelectTrigger className="w-full md:w-48">
                                    <SelectValue placeholder="Type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Tous les types</SelectItem>
                                    {Object.entries(typesConversation).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>{label}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <div className="flex gap-2">
                                <Button onClick={handleFilter} variant="outline">
                                    <Filter className="h-4 w-4 mr-2" />
                                    Filtrer
                                </Button>
                                <Button onClick={clearFilters} variant="ghost">
                                    Effacer
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Liste des conversations */}
                <div className="space-y-4">
                    {!conversations.data || conversations.data.length === 0 ? (
                        <Card>
                            <CardContent className="p-8 text-center">
                                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                    Aucune conversation trouvée
                                </h3>
                                <p className="text-gray-600 dark:text-gray-400 mb-4">
                                    Vous n'avez pas encore de conversation ou aucune ne correspond à vos critères.
                                </p>
                                <Link href={route('client.conversations.create')}>
                                    <Button>
                                        <Plus className="h-4 w-4 mr-2" />
                                        Créer une conversation
                                    </Button>
                                </Link>
                            </CardContent>
                        </Card>
                    ) : (
                        conversations.data.map((conversation) => (
                            <Card key={conversation.id} className="hover:shadow-md transition-shadow">
                                <CardContent className="p-6">
                                    <div className="flex justify-between items-start mb-4">
                                        <div className="flex-1">
                                            <div className="flex items-center gap-3 mb-2">
                                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                                    {conversation.sujet}
                                                </h3>
                                                <Badge variant={conversation.statut_color as any}>
                                                    {getStatusIcon(conversation.statut)}
                                                    <span className="ml-1">{conversation.statut_formate}</span>
                                                </Badge>
                                                {conversation.messages_non_lus_client > 0 && (
                                                    <Badge variant="destructive">
                                                        {conversation.messages_non_lus_client} nouveau{conversation.messages_non_lus_client > 1 ? 'x' : ''}
                                                    </Badge>
                                                )}
                                            </div>
                                            
                                            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-2">
                                                <div className="flex items-center gap-1">
                                                    <Store className="h-4 w-4" />
                                                    <span>{conversation.marchand.nom_entreprise}</span>
                                                </div>
                                                <div className="flex items-center gap-1">
                                                    <MessageSquare className="h-4 w-4" />
                                                    <span>{conversation.nombre_messages_total} message{conversation.nombre_messages_total > 1 ? 's' : ''}</span>
                                                </div>
                                                <div className="flex items-center gap-1">
                                                    <Calendar className="h-4 w-4" />
                                                    <span>{formatDate(conversation.date_creation)}</span>
                                                </div>
                                            </div>

                                            {conversation.commande_principale && (
                                                <div className="flex items-center gap-1 text-sm text-gray-500 mb-2">
                                                    <Package className="h-4 w-4" />
                                                    <span>Commande {conversation.commande_principale.numero_commande}</span>
                                                </div>
                                            )}

                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                <span className="font-medium">Type :</span> {conversation.type_formate}
                                            </p>
                                        </div>

                                        <div className="flex items-center gap-2">
                                            <Link href={route('client.conversations.show', conversation.id)}>
                                                <Button variant="outline" size="sm">
                                                    Voir conversation
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>

                                    {conversation.messages && conversation.messages.length > 0 && (
                                        <div className="border-t pt-3 mt-3">
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                <span className="font-medium">Dernier message :</span> {' '}
                                                {conversation.messages[0]?.message?.substring(0, 100) || ''}
                                                {conversation.messages[0]?.message?.length > 100 && '...'}
                                            </p>
                                            {conversation.date_dernier_message && (
                                                <p className="text-xs text-gray-500 mt-1">
                                                    {formatDate(conversation.date_dernier_message)}
                                                </p>
                                            )}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        ))
                    )}
                </div>

                {/* Pagination */}
                {conversations.meta && conversations.meta.last_page > 1 && (
                    <div className="flex justify-center">
                        <p className="text-sm text-gray-500">
                            Page {conversations.meta.current_page} sur {conversations.meta.last_page}
                        </p>
                    </div>
                )}
            </div>
        </ClientDashboardLayout>
    );
}
