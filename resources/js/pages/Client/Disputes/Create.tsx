import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import ClientDashboardLayout from '@/layouts/client-dashboard-layout';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Alert, AlertDescription } from '@/Components/ui/alert';
import {
    ArrowLeft,
    Upload,
    X,
    FileText,
    Package,
    AlertCircle,
    Info
} from 'lucide-react';

interface Commande {
    id: number;
    numero_commande: string;
    montant_total_ttc: number;
    statut_global: string;
    date_commande: string;
    sous_commandes: Array<{
        id: number;
        numero_sous_commande: string;
        marchand: {
            nom_entreprise: string;
        };
    }>;
}

interface Props {
    commandes: Commande[];
    commandeSelectionnee?: Commande;
    typesLitige: Record<string, string>;
    maxFileSize: number;
}

export default function DisputeCreate({ commandes, commandeSelectionnee, typesLitige, maxFileSize }: Props) {
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

    const { data, setData, post, processing, errors, reset } = useForm({
        commande_principale_id: commandeSelectionnee?.id || '',
        sous_commande_id: '',
        type_litige: '',
        sujet: '',
        description: '',
        solution_souhaitee: '',
        montant_conteste: '',
        pieces_jointes: [] as File[]
    });

    const selectedCommande = commandes.find(c => c.id === Number(data.commande_principale_id));

    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(event.target.files || []);
        const validFiles = files.filter(file => {
            const isValidType = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type);
            const isValidSize = file.size <= maxFileSize * 1024; // Convert KB to bytes
            return isValidType && isValidSize;
        });

        if (selectedFiles.length + validFiles.length <= 5) {
            const newFiles = [...selectedFiles, ...validFiles];
            setSelectedFiles(newFiles);
            setData('pieces_jointes', newFiles);
        }
    };

    const removeFile = (index: number) => {
        const newFiles = selectedFiles.filter((_, i) => i !== index);
        setSelectedFiles(newFiles);
        setData('pieces_jointes', newFiles);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        post(route('client.disputes.store'), {
            forceFormData: true,
            onSuccess: () => {
                // La redirection sera gérée par le contrôleur
            },
            onError: (errors) => {
                console.error('Erreurs de validation:', errors);
            }
        });
    };

    const formatAmount = (amount: number) => {
        return new Intl.NumberFormat('fr-FR').format(amount) + ' FCFA';
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('fr-FR');
    };

    return (
        <ClientDashboardLayout>
            <Head title="Créer un Litige" />

            <div className="max-w-4xl mx-auto space-y-6">
                {/* En-tête */}
                <div className="flex items-center gap-4">
                    <Link href={route('client.disputes.index')}>
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Retour
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                            Créer un Litige
                        </h1>
                        <p className="text-gray-600 dark:text-gray-400">
                            Signalez un problème avec votre commande
                        </p>
                    </div>
                </div>

                {/* Informations importantes */}
                <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                        <div className="space-y-2">
                            <p>Avant de créer un litige, assurez-vous d'avoir contacté le marchand directement.</p>
                            <p>
                                <Link href={route('client.conversations.index')} className="text-blue-600 hover:text-blue-800 underline">
                                    Voir mes conversations avec les marchands
                                </Link> ou{' '}
                                <Link href={route('client.conversations.create')} className="text-blue-600 hover:text-blue-800 underline">
                                    créer une nouvelle conversation
                                </Link>
                            </p>
                            <p className="text-sm text-gray-600">Un litige ne peut être créé que pour des commandes payées.</p>
                        </div>
                    </AlertDescription>
                </Alert>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Sélection de la commande */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Package className="h-5 w-5" />
                                Commande concernée
                            </CardTitle>
                            <CardDescription>
                                Sélectionnez la commande pour laquelle vous souhaitez créer un litige
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label htmlFor="commande_principale_id">Commande *</Label>
                                <Select
                                    value={String(data.commande_principale_id)}
                                    onValueChange={(value) => setData('commande_principale_id', Number(value))}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Sélectionnez une commande" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {commandes.map((commande) => (
                                            <SelectItem key={commande.id} value={String(commande.id)}>
                                                <div className="flex flex-col">
                                                    <span className="font-medium">
                                                        {commande.numero_commande}
                                                    </span>
                                                    <span className="text-sm text-gray-500">
                                                        {formatAmount(commande.montant_total_ttc)} - {formatDate(commande.date_commande)}
                                                    </span>
                                                </div>
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.commande_principale_id && (
                                    <p className="text-sm text-red-600 mt-1">{errors.commande_principale_id}</p>
                                )}
                            </div>

                            {selectedCommande && selectedCommande.sous_commandes.length > 1 && (
                                <div>
                                    <Label htmlFor="sous_commande_id">Sous-commande spécifique (optionnel)</Label>
                                    <Select
                                        value={String(data.sous_commande_id || '0')}
                                        onValueChange={(value) => setData('sous_commande_id', value === '0' ? '' : Number(value))}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Toute la commande" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="0">Toute la commande</SelectItem>
                                            {selectedCommande.sous_commandes.map((sousCommande) => (
                                                <SelectItem key={sousCommande.id} value={String(sousCommande.id)}>
                                                    {sousCommande.numero_sous_commande} - {sousCommande.marchand.nom_entreprise}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Détails du litige */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="h-5 w-5" />
                                Détails du litige
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label htmlFor="type_litige">Type de problème *</Label>
                                <Select
                                    value={data.type_litige}
                                    onValueChange={(value) => setData('type_litige', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Sélectionnez le type de problème" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {Object.entries(typesLitige).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>{label}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.type_litige && (
                                    <p className="text-sm text-red-600 mt-1">{errors.type_litige}</p>
                                )}
                            </div>

                            <div>
                                <Label htmlFor="sujet">Sujet *</Label>
                                <Input
                                    id="sujet"
                                    value={data.sujet}
                                    onChange={(e) => setData('sujet', e.target.value)}
                                    placeholder="Résumé du problème en quelques mots"
                                    maxLength={255}
                                />
                                {errors.sujet && (
                                    <p className="text-sm text-red-600 mt-1">{errors.sujet}</p>
                                )}
                            </div>

                            <div>
                                <Label htmlFor="description">Description détaillée *</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Décrivez le problème en détail : que s'est-il passé ? Quand ? Dans quelles circonstances ?"
                                    rows={6}
                                    maxLength={2000}
                                />
                                <p className="text-sm text-gray-500 mt-1">
                                    {data.description.length}/2000 caractères
                                </p>
                                {errors.description && (
                                    <p className="text-sm text-red-600 mt-1">{errors.description}</p>
                                )}
                            </div>

                            <div>
                                <Label htmlFor="solution_souhaitee">Solution souhaitée</Label>
                                <Textarea
                                    id="solution_souhaitee"
                                    value={data.solution_souhaitee}
                                    onChange={(e) => setData('solution_souhaitee', e.target.value)}
                                    placeholder="Quelle solution attendez-vous ? (remboursement, échange, réparation, etc.)"
                                    rows={3}
                                    maxLength={1000}
                                />
                                {errors.solution_souhaitee && (
                                    <p className="text-sm text-red-600 mt-1">{errors.solution_souhaitee}</p>
                                )}
                            </div>

                            <div>
                                <Label htmlFor="montant_conteste">Montant contesté (FCFA)</Label>
                                <Input
                                    id="montant_conteste"
                                    type="number"
                                    value={data.montant_conteste}
                                    onChange={(e) => setData('montant_conteste', e.target.value)}
                                    placeholder="Montant en FCFA"
                                    min="0"
                                />
                                {errors.montant_conteste && (
                                    <p className="text-sm text-red-600 mt-1">{errors.montant_conteste}</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Pièces jointes */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Upload className="h-5 w-5" />
                                Pièces jointes
                            </CardTitle>
                            <CardDescription>
                                Ajoutez des photos, documents ou preuves (max 5 fichiers, {maxFileSize/1024}MB chacun)
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <input
                                    type="file"
                                    multiple
                                    accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                                    onChange={handleFileUpload}
                                    className="hidden"
                                    id="file-upload"
                                />
                                <Label htmlFor="file-upload" className="cursor-pointer">
                                    <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                                        <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            Cliquez pour sélectionner des fichiers
                                        </p>
                                        <p className="text-xs text-gray-500 mt-1">
                                            JPG, PNG, PDF, DOC, DOCX - Max {maxFileSize/1024}MB
                                        </p>
                                    </div>
                                </Label>
                            </div>

                            {selectedFiles.length > 0 && (
                                <div className="space-y-2">
                                    <Label>Fichiers sélectionnés :</Label>
                                    {selectedFiles.map((file, index) => (
                                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                                            <span className="text-sm truncate">{file.name}</span>
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => removeFile(index)}
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    ))}
                                </div>
                            )}

                            {errors.pieces_jointes && (
                                <p className="text-sm text-red-600">{errors.pieces_jointes}</p>
                            )}
                        </CardContent>
                    </Card>

                    {/* Actions */}
                    <div className="flex justify-end gap-4">
                        <Link href={route('client.disputes.index')}>
                            <Button type="button" variant="outline">
                                Annuler
                            </Button>
                        </Link>
                        <Button type="submit" disabled={processing}>
                            {processing ? 'Création...' : 'Créer le litige'}
                        </Button>
                    </div>
                </form>
            </div>
        </ClientDashboardLayout>
    );
}
