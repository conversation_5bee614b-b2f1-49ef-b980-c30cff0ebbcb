/**
 * Configuration des URLs d'API pour le projet Lorelei
 * 
 * Ce fichier centralise toutes les configurations d'API pour permettre
 * une gestion dynamique des endpoints selon l'environnement
 */

// Configuration des URLs de base selon l'environnement
const getApiBaseUrls = () => {
  // En développement, utiliser les variables d'environnement ou les valeurs par défaut
  const isDevelopment = import.meta.env.DEV;
  
  return {
    // API locale du projet lorrelei (frontend e-commerce)
    local: '/api',
    
    // API du projet admin_marchand_lorrelei (backend admin)
    admin: import.meta.env.VITE_ADMIN_API_URL || 
           (isDevelopment ? 'http://localhost:8001/api' : '/admin-api'),
    
    // URL de base pour les images et assets
    assets: import.meta.env.VITE_ASSETS_URL || 
            (isDevelopment ? 'http://localhost:8001' : ''),
  };
};

// Instance de configuration
const apiConfig = getApiBaseUrls();

/**
 * Construit une URL complète pour l'API admin
 * 
 * @param endpoint - L'endpoint à appeler (ex: 'marchands/4')
 * @returns L'URL complète
 */
export const buildAdminApiUrl = (endpoint: string): string => {
  const baseUrl = apiConfig.admin;
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${baseUrl}/${cleanEndpoint}`;
};

/**
 * Construit une URL complète pour l'API locale
 * 
 * @param endpoint - L'endpoint à appeler (ex: 'categories')
 * @returns L'URL complète
 */
export const buildLocalApiUrl = (endpoint: string): string => {
  const baseUrl = apiConfig.local;
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${baseUrl}/${cleanEndpoint}`;
};

/**
 * Construit une URL complète pour les assets
 * 
 * @param path - Le chemin vers l'asset (ex: 'images/products/image.jpg')
 * @returns L'URL complète
 */
export const buildAssetUrl = (path: string): string => {
  if (!path) return '';
  
  // Si l'URL est déjà complète, la retourner telle quelle
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }
  
  const baseUrl = apiConfig.assets;
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  return baseUrl ? `${baseUrl}/${cleanPath}` : `/${cleanPath}`;
};

/**
 * Configuration des endpoints spécifiques
 */
export const API_ENDPOINTS = {
  // Endpoints pour les marchands (via API admin)
  MARCHANDS: {
    GET_BY_ID: (id: string) => buildAdminApiUrl(`marchands/${id}`),
    GET_BY_SLUG: (slug: string) => buildAdminApiUrl(`marchands/slug/${slug}`),
    GET_STATS: (id: string) => buildAdminApiUrl(`marchands/${id}/stats`),
    GET_PRODUCTS: (id: string) => buildAdminApiUrl(`marchands/${id}/produits`),
  },
  
  // Endpoints pour les produits (API locale et admin)
  PRODUCTS: {
    // API locale pour les produits publics
    GET_ALL: () => buildLocalApiUrl('produits'),
    GET_BY_ID: (id: string) => buildLocalApiUrl(`produits/${id}`),
    GET_BY_SLUG: (slug: string) => buildLocalApiUrl(`produits/slug/${slug}`),
    GET_BY_CATEGORY: (categoryId: string) => buildLocalApiUrl(`produits/categorie/${categoryId}`),
    GET_FEATURED: (limit?: number) => buildLocalApiUrl(`produits/featured${limit ? `/${limit}` : ''}`),
    SEARCH: () => buildLocalApiUrl('produits/search'),
    
    // API admin pour les produits des marchands
    GET_BY_MARCHAND: (marchandId: string) => buildAdminApiUrl(`marchands/${marchandId}/produits`),
  },
  
  // Endpoints pour les catégories (API locale)
  CATEGORIES: {
    GET_ALL: () => buildLocalApiUrl('categories'),
    GET_MAIN: () => buildLocalApiUrl('categories/main'),
    GET_BY_ID: (id: string) => buildLocalApiUrl(`categories/${id}`),
    GET_PARENTS: (id: string) => buildLocalApiUrl(`categories/${id}/parents`),
  },
  
  // Endpoints pour les bannières (API locale)
  BANNERS: {
    GET_ALL: () => buildLocalApiUrl('banners'),
    GET_BY_POSITION: (position: string) => buildLocalApiUrl(`banners/position/${position}`),
  },
} as const;

/**
 * Configuration des timeouts et options de fetch
 */
export const FETCH_CONFIG = {
  DEFAULT_TIMEOUT: 10000, // 10 secondes
  CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
  
  // Options par défaut pour fetch
  DEFAULT_OPTIONS: {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  },
  
  // Options pour les requêtes vers l'API admin
  ADMIN_API_OPTIONS: {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    },
  },
} as const;

/**
 * Utilitaire pour créer une requête fetch avec configuration par défaut
 * 
 * @param url - URL à appeler
 * @param options - Options supplémentaires pour fetch
 * @returns Promise de la réponse fetch
 */
export const createFetch = async (
  url: string, 
  options: RequestInit = {}
): Promise<Response> => {
  const isAdminApi = url.includes(apiConfig.admin);
  const defaultOptions = isAdminApi ? FETCH_CONFIG.ADMIN_API_OPTIONS : FETCH_CONFIG.DEFAULT_OPTIONS;
  
  const mergedOptions: RequestInit = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  // Ajouter un timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), FETCH_CONFIG.DEFAULT_TIMEOUT);
  
  try {
    const response = await fetch(url, {
      ...mergedOptions,
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
};

export default apiConfig;
