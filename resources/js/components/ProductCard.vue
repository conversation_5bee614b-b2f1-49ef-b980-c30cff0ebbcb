<template>
  <div class="product-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
    <!-- Image du produit avec miniatures -->
    <div class="relative aspect-square overflow-hidden">
      <img
        :src="mainThumbnail"
        :srcset="mainSrcSet"
        :alt="product.name"
        class="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
        sizes="(max-width: 640px) 150px, (max-width: 1024px) 300px, 600px"
        loading="lazy"
      />
      
      <!-- Badge de promotion -->
      <div
        v-if="product.isOnSale()"
        class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-semibold"
      >
        -{{ product.getDiscountPercentage() }}%
      </div>

      <!-- Bouton d'action rapide -->
      <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <button
          @click="addToCart"
          class="bg-white text-gray-800 p-2 rounded-full shadow-md hover:bg-gray-50 transition-colors duration-200"
          :title="$t('product.addToCart')"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </button>
      </div>

      <!-- Galerie d'images au survol -->
      <div
        v-if="galleryThumbnails.length > 0"
        class="absolute bottom-2 left-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      >
        <div
          v-for="(thumbnail, index) in galleryThumbnails.slice(0, 3)"
          :key="index"
          class="flex-1 aspect-square rounded overflow-hidden"
        >
          <img
            :src="thumbnail"
            :alt="`${product.name} - Image ${index + 2}`"
            class="w-full h-full object-cover cursor-pointer hover:opacity-80 transition-opacity duration-200"
            @click="showImageGallery(index + 1)"
          />
        </div>
        <div
          v-if="galleryThumbnails.length > 3"
          class="flex-1 aspect-square rounded bg-black bg-opacity-50 flex items-center justify-center text-white text-xs font-semibold cursor-pointer hover:bg-opacity-60 transition-colors duration-200"
          @click="showImageGallery(4)"
        >
          +{{ galleryThumbnails.length - 3 }}
        </div>
      </div>
    </div>

    <!-- Informations du produit -->
    <div class="p-4">
      <!-- Nom du produit -->
      <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
        {{ product.name }}
      </h3>

      <!-- Prix -->
      <div class="flex items-center space-x-2 mb-2">
        <span class="text-xl font-bold text-gray-900">
          {{ product.formattedPrice() }}
        </span>
        <span
          v-if="product.isOnSale()"
          class="text-sm text-gray-500 line-through"
        >
          {{ product.formattedOriginalPrice() }}
        </span>
      </div>

      <!-- Note et avis -->
      <div class="flex items-center space-x-2 mb-3">
        <div class="flex items-center">
          <div class="flex text-yellow-400">
            <svg
              v-for="star in 5"
              :key="star"
              class="w-4 h-4"
              :class="star <= product.rating ? 'fill-current' : 'fill-gray-300'"
              viewBox="0 0 20 20"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          </div>
          <span class="text-sm text-gray-600 ml-1">
            ({{ product.reviews }})
          </span>
        </div>
      </div>

      <!-- Statut du stock -->
      <div class="flex items-center justify-between">
        <span
          :class="product.inStock ? 'text-green-600' : 'text-red-600'"
          class="text-sm font-medium"
        >
          {{ product.inStock ? $t('product.inStock') : $t('product.outOfStock') }}
        </span>
        
        <!-- Zones de livraison -->
        <span
          v-if="product.availableZones > 0"
          class="text-xs text-gray-500"
        >
          {{ $t('product.deliveryZones', { count: product.availableZones }) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Product } from '@/models/Product';
import { useProductThumbnails } from '@/composables/useThumbnails';

interface Props {
  product: Product;
}

const props = defineProps<Props>();

// Utilisation du composable pour les miniatures
const {
  getMainThumbnail,
  getGalleryThumbnails,
  generateSrcSet
} = useProductThumbnails(props.product.thumbnailUrls, props.product.imageUrls);

// Miniature principale
const mainThumbnail = computed(() => getMainThumbnail('medium'));

// Miniatures pour la galerie
const galleryThumbnails = computed(() => getGalleryThumbnails('small'));

// SrcSet pour l'image principale
const mainSrcSet = computed(() => generateSrcSet(0));

// Méthodes
const addToCart = () => {
  // Logique d'ajout au panier
  console.log('Ajout au panier:', props.product.name);
};

const showImageGallery = (startIndex: number) => {
  // Logique pour afficher la galerie d'images
  console.log('Afficher la galerie à partir de l\'image:', startIndex);
};
</script>

<style scoped>
.product-card {
  @apply group;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}
</style>
