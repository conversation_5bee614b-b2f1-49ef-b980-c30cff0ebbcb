import React, { useState } from 'react';
import { Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTranslation } from '@/hooks/use-translation';

// Type pour les langues supportées
type Locale = 'fr' | 'en';

/**
 * Composant pour changer la langue de l'application
 */
export default function LanguageSwitcher() {
  const { currentLocale, setLocale } = useTranslation();

  // Langues disponibles
  const languages: Array<{ code: Locale; name: string; flag: string; display: string }> = [
    { code: 'fr', name: 'Français', flag: '🇫🇷', display: 'FR' },
    { code: 'en', name: 'English', flag: '🇬🇧', display: 'EN' },
  ];

  /**
   * Change la langue de l'application
   *
   * @param locale - Code de la langue
   */
  const handleLanguageChange = (locale: Locale) => {
    setLocale(locale);

    // Recharger la page pour appliquer les changements
    // En production, vous pourriez vouloir utiliser une approche plus sophistiquée
    window.location.reload();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Globe className="h-5 w-5" />
          <span className="sr-only">Changer de langue</span>
          <span className="absolute -bottom-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-[10px] font-bold text-primary-foreground">
            {languages.find(lang => lang.code === currentLocale)?.display || 'FR'}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className={`flex cursor-pointer items-center gap-2 ${
              currentLocale === language.code ? 'bg-muted font-medium' : ''
            }`}
          >
            <span className="mr-1">{language.flag}</span>
            {language.name}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
