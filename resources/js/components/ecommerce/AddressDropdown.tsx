import React, { useState, useEffect } from 'react';
import { Link } from '@inertiajs/react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { MapPin, PlusCircle } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import AddressForm from './AddressForm';

interface Address {
  id: string;
  rue: string;
  ville: string;
  etat: string;
  pays: string;
  codePostal: string;
  type: string;
}

interface AddressDropdownProps {
  addresses: Address[];
  triggerClassName?: string;
}

export default function AddressDropdown({ addresses, triggerClassName = '' }: AddressDropdownProps) {
  const [isAddressFormOpen, setIsAddressFormOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Empêcher la fermeture du modal lorsqu'on clique à l'intérieur
  const handleDialogOpenChange = (open: boolean) => {
    // Si on essaie de fermer le modal, vérifier si c'est intentionnel
    if (!open && isAddressFormOpen) {
      // Fermer le modal seulement si l'utilisateur a explicitement demandé à le fermer
      setIsAddressFormOpen(false);
    } else {
      setIsAddressFormOpen(open);
    }
  };

  // Fermer le dropdown lorsque le formulaire d'adresse est ouvert
  useEffect(() => {
    if (!isDropdownOpen) {
      setIsAddressFormOpen(false);
    }
  }, [isDropdownOpen]);

  const handleAddressSuccess = () => {
    setIsAddressFormOpen(false);
    window.location.reload();
  };

  return (
    <>
      <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className={triggerClassName}>
            <MapPin className="h-5 w-5" />
            <span className="sr-only">Adresses</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-72">
          <DropdownMenuLabel>Mes adresses</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {addresses.length > 0 ? (
            <>
              {addresses.map((address) => (
                <DropdownMenuItem key={address.id} className="flex flex-col items-start p-3">
                  <div className="flex w-full items-start justify-between">
                    <div>
                      <p className="font-medium">{address.type}</p>
                      <p className="text-xs text-muted-foreground">{address.rue}</p>
                      <p className="text-xs text-muted-foreground">
                        {address.codePostal} {address.ville}, {address.pays}
                      </p>
                    </div>
                  </div>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
            </>
          ) : (
            <div className="flex flex-col items-center justify-center p-4 text-center">
              <MapPin className="mb-2 h-8 w-8 text-muted-foreground/50" />
              <p className="mb-1 text-sm font-medium">Aucune adresse</p>
              <p className="mb-3 text-xs text-muted-foreground">
                Vous n'avez pas encore ajouté d'adresse.
              </p>
            </div>
          )}

          <Button
            variant="outline"
            size="sm"
            className="m-2 flex w-[calc(100%-16px)] items-center justify-center gap-1"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsAddressFormOpen(true);
              setIsDropdownOpen(true);
            }}
          >
            <PlusCircle className="h-4 w-4" />
            <span>Ajouter une adresse</span>
          </Button>

          <Dialog open={isAddressFormOpen} onOpenChange={handleDialogOpenChange}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Ajouter une adresse</DialogTitle>
              </DialogHeader>
              <AddressForm onSuccess={handleAddressSuccess} defaultType='Livraison' disableTypeSelection={true} />
            </DialogContent>
          </Dialog>

          <DropdownMenuItem asChild className="p-0">
            <Link
              href={route('my-account')}
              className="flex w-full items-center justify-center p-2 text-xs text-muted-foreground"
            >
              Gérer toutes mes adresses
            </Link>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
}
