import { DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { type User } from '@/types';
import { Link } from '@inertiajs/react';
import { LogOut, User as UserIcon, LayoutDashboard, ShoppingBag, MessageCircle, AlertTriangle, Settings } from 'lucide-react';

interface NotificationData {
  conversations_non_lues: number;
  disputes_non_lues: number;
  total_non_lus: number;
}

interface UserMenuEcommerceProps {
  user: User;
  notifications?: NotificationData;
}

export default function UserMenuEcommerce({ user, notifications }: UserMenuEcommerceProps) {
  return (
    <>
      <DropdownMenuLabel className="p-0 font-normal">
        <div className="flex flex-col px-3 py-2">
          <span className="font-medium">{user.name}</span>
          <span className="text-xs text-muted-foreground">{user.email}</span>
        </div>
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuGroup>
        <DropdownMenuItem asChild>
          <Link href={route('dashboard')} className="flex w-full cursor-pointer items-center">
            <LayoutDashboard className="mr-2 h-4 w-4" />
            Dashboard
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={route('dashboard.orders')} className="flex w-full cursor-pointer items-center">
            <ShoppingBag className="mr-2 h-4 w-4" />
            Mes commandes
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={route('client.conversations.index')} className="flex w-full cursor-pointer items-center justify-between">
            <div className="flex items-center">
              <MessageCircle className="mr-2 h-4 w-4" />
              Mes conversations
            </div>
            {notifications && notifications.conversations_non_lues > 0 && (
              <Badge variant="destructive" className="text-xs h-5 min-w-[20px] flex items-center justify-center">
                {notifications.conversations_non_lues > 9 ? '9+' : notifications.conversations_non_lues}
              </Badge>
            )}
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={route('client.disputes.index')} className="flex w-full cursor-pointer items-center justify-between">
            <div className="flex items-center">
              <AlertTriangle className="mr-2 h-4 w-4" />
              Mes litiges
            </div>
            {notifications && notifications.disputes_non_lues > 0 && (
              <Badge variant="destructive" className="text-xs h-5 min-w-[20px] flex items-center justify-center">
                {notifications.disputes_non_lues > 9 ? '9+' : notifications.disputes_non_lues}
              </Badge>
            )}
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={route('dashboard.profile')} className="flex w-full cursor-pointer items-center">
            <UserIcon className="mr-2 h-4 w-4" />
            Mon profil
          </Link>
        </DropdownMenuItem>
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <DropdownMenuItem asChild>
        <Link method="post" href={route('logout')} className="flex w-full cursor-pointer items-center text-destructive">
          <LogOut className="mr-2 h-4 w-4" />
          Déconnexion
        </Link>
      </DropdownMenuItem>
    </>
  );
}
