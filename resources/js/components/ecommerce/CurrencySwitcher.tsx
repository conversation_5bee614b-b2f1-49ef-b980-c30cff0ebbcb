import React, { useState, useEffect } from 'react';
import { DollarSign } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { CurrencyService, Currency } from '@/services/CurrencyService';

/**
 * Composant pour changer la devise de l'application
 */
export default function CurrencySwitcher() {
  const [currentCurrency, setCurrentCurrency] = useState<string>('FCFA');
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const currencyService = new CurrencyService();

  // Charger les devises et la devise actuelle au chargement du composant
  useEffect(() => {
    const loadCurrencies = async () => {
      try {
        // Récupérer toutes les devises
        const allCurrencies = await currencyService.getAllCurrencies();
        if (allCurrencies.length > 0) {
          setCurrencies(allCurrencies);
        }

        // Récupérer la devise actuelle
        const current = await currencyService.getCurrentCurrency();
        if (current) {
          setCurrentCurrency(current.code);
        }
      } catch (error) {
        console.error('Erreur lors du chargement des devises:', error);
      }
    };

    loadCurrencies();
  }, []);

  /**
   * Change la devise de l'application et filtre les produits par devise
   *
   * @param currencyCode - Code de la devise
   */
  const handleCurrencyChange = async (currencyCode: string) => {
    try {
      // Mettre à jour la devise dans le localStorage
      localStorage.setItem('currency', currencyCode);
      setCurrentCurrency(currencyCode);

      // Envoyer la requête au serveur pour mettre à jour la session
      await currencyService.setCurrentCurrency(currencyCode);

      // Ajouter le paramètre de devise à l'URL actuelle
      const url = new URL(window.location.href);
      url.searchParams.set('currency', currencyCode);

      // Rediriger vers la nouvelle URL avec le filtre de devise
      window.location.href = url.toString();
    } catch (error) {
      console.error('Erreur lors du changement de devise:', error);
    }
  };

  // Obtenir le symbole de la devise actuelle
  const getCurrentSymbol = (): string => {
    const currency = currencies.find(c => c.code === currentCurrency);
    if (currency) return currency.symbol;

    // Fallback pour les symboles courants
    switch (currentCurrency) {
      case 'EUR': return '€';
      case 'USD': return '$';
      case 'GBP': return '£';
      case 'FCFA':
      case 'XAF':
      case 'XOF':
      default: return 'CFA';
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <DollarSign className="h-5 w-5" />
          <span className="sr-only">Changer de devise</span>
          <span className="absolute -bottom-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-[8px] font-bold text-primary-foreground">
            {currentCurrency === 'FCFA' || currentCurrency === 'XAF' || currentCurrency === 'XOF'
              ? 'CFA'
              : getCurrentSymbol()}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {currencies.length > 0 ? (
          currencies.map((currency) => (
            <DropdownMenuItem
              key={currency.code}
              onClick={() => handleCurrencyChange(currency.code)}
              className={`flex cursor-pointer items-center gap-2 ${
                currentCurrency === currency.code ? 'bg-muted font-medium' : ''
              }`}
            >
              <span className="mr-1">{currency.symbol}</span>
              {currency.name}
            </DropdownMenuItem>
          ))
        ) : (
          <DropdownMenuItem disabled>Chargement des devises...</DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
