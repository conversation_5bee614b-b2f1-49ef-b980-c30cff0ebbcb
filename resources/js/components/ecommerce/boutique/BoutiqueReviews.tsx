import React, { useState, useEffect } from 'react';
import { ChevronDown, Filter, Plus, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from '@/hooks/use-translation';
import { boutiqueReviewService, BoutiqueReview, BoutiqueReviewStats, BoutiqueReviewsResponse } from '@/services/BoutiqueReviewService';
import BoutiqueRating from './BoutiqueRating';
import BoutiqueReviewItem from './BoutiqueReviewItem';
import BoutiqueReviewForm from './BoutiqueReviewForm';

interface BoutiqueReviewsProps {
  marchandId: string;
  className?: string;
}

export default function BoutiqueReviews({ marchandId, className = '' }: BoutiqueReviewsProps) {
  const { translate } = useTranslation();
  const [reviews, setReviews] = useState<BoutiqueReview[]>([]);
  const [stats, setStats] = useState<BoutiqueReviewStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [reviewsResponse, setReviewsResponse] = useState<BoutiqueReviewsResponse | null>(null);
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [filterRating, setFilterRating] = useState<string>('');
  const [filterVerified, setFilterVerified] = useState<boolean | undefined>();
  const [showReviewForm, setShowReviewForm] = useState(false);

  const loadReviews = async () => {
    try {
      setIsLoading(true);
      const params = {
        page: currentPage,
        per_page: 5,
        sort_by: sortBy,
        sort_order: sortOrder,
        ...(filterRating && { rating: filterRating }),
        ...(filterVerified !== undefined && { verified: filterVerified }),
      };

      const result = await boutiqueReviewService.getBoutiqueReviews(marchandId, params);
      setReviewsResponse(result);
      setReviews(result.data);
      setTotalPages(result.last_page);
    } catch (error) {
      console.error('Erreur lors du chargement des avis:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await boutiqueReviewService.getBoutiqueStats(marchandId);
      setStats(statsData);
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    }
  };

  useEffect(() => {
    loadReviews();
  }, [marchandId, currentPage, sortBy, sortOrder, filterRating, filterVerified]);

  useEffect(() => {
    loadStats();
  }, [marchandId]);

  const handleVote = async (reviewId: number, voteType: 'like' | 'dislike') => {
    try {
      await boutiqueReviewService.voteReview(reviewId, voteType);
      loadReviews(); // Recharger pour mettre à jour les votes
    } catch (error) {
      console.error('Erreur lors du vote:', error);
    }
  };

  const handleReport = async (reviewId: number, reason: string) => {
    try {
      await boutiqueReviewService.reportReview(reviewId, reason);
      // Optionnel : afficher un message de confirmation
    } catch (error) {
      console.error('Erreur lors du signalement:', error);
    }
  };

  const handleReviewSubmitted = () => {
    setShowReviewForm(false);
    loadReviews();
    loadStats();
  };

  if (isLoading && !reviews.length) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle className="text-lg sm:text-xl mb-2">
              {translate('reviews.customer_reviews')}
            </CardTitle>
            {stats && (
              <BoutiqueRating
                rating={stats.average_rating}
                totalReviews={stats.total_reviews}
                size="md"
              />
            )}
          </div>

          <Dialog open={showReviewForm} onOpenChange={setShowReviewForm}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                {translate('reviews.write_review')}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <BoutiqueReviewForm
                marchandId={marchandId}
                onSuccess={handleReviewSubmitted}
                onCancel={() => setShowReviewForm(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Statistiques détaillées */}
        {stats && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3">
            {Object.entries(stats.rating_stats.distribution).reverse().map(([rating, count]) => (
              <div key={rating} className="flex items-center gap-2 text-sm">
                <div className="flex items-center gap-1">
                  <span className="font-medium">{rating}</span>
                  <Star className="h-3 w-3 text-amber-400 fill-current" />
                </div>
                <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-amber-400 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: stats.rating_stats.total > 0
                        ? `${(count / stats.rating_stats.total) * 100}%`
                        : '0%'
                    }}
                  />
                </div>
                <span className="text-gray-600 dark:text-gray-400 min-w-[2rem] text-right">
                  {count}
                </span>
              </div>
            ))}
          </div>
        )}

        {/* Filtres et tri */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder={translate('reviews.sort_by')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="created_at">{translate('reviews.sort_newest')}</SelectItem>
              <SelectItem value="rating">{translate('reviews.sort_rating')}</SelectItem>
              <SelectItem value="likes">{translate('reviews.sort_helpful')}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterRating} onValueChange={setFilterRating}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder={translate('reviews.filter_rating')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">{translate('reviews.all_ratings')}</SelectItem>
              <SelectItem value="5">5 {translate('reviews.stars')}</SelectItem>
              <SelectItem value="4">4 {translate('reviews.stars')}</SelectItem>
              <SelectItem value="3">3 {translate('reviews.stars')}</SelectItem>
              <SelectItem value="2">2 {translate('reviews.stars')}</SelectItem>
              <SelectItem value="1">1 {translate('reviews.star')}</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            onClick={() => setFilterVerified(filterVerified ? undefined : true)}
            className={`flex items-center gap-2 ${filterVerified ? 'bg-primary/10' : ''}`}
          >
            <Filter className="h-4 w-4" />
            {translate('reviews.verified_only')}
          </Button>
        </div>

        {/* Liste des avis */}
        <div className="space-y-4">
          {reviews.length > 0 ? (
            reviews.map((review) => (
              <BoutiqueReviewItem
                key={review.id}
                review={review}
                onVote={handleVote}
                onReport={handleReport}
              />
            ))
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-500 dark:text-gray-400 mb-4">
                {translate('reviews.no_reviews_yet')}
              </div>
              <Button onClick={() => setShowReviewForm(true)}>
                {translate('reviews.be_first_to_review')}
              </Button>
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              {translate('common.previous')}
            </Button>

            <div className="flex items-center gap-2">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                  >
                    {page}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
            >
              {translate('common.next')}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
