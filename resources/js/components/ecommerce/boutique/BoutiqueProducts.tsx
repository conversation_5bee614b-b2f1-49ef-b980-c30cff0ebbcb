import { Link } from '@inertiajs/react';
import { Marchand } from '@/models/Marchand';
import { Product } from '@/models/Product';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight, Package } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import CardProduit from '../CardProduit';

interface BoutiqueProductsProps {
  marchand: Marchand;
  products: Product[];
  showViewAll?: boolean;
  title?: string;
}

export default function BoutiqueProducts({
  marchand,
  products,
  showViewAll = false,
  title
}: BoutiqueProductsProps) {
  const { translate } = useTranslation();
  const defaultTitle = translate('common.shop_products');

  if (products.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>{title || defaultTitle}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <Package className="h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {translate('pages.boutique.no_products')}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {translate('pages.boutique.no_products_description')}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>{title || defaultTitle}</span>
          </CardTitle>

          {showViewAll && products.length > 0 && (
            <Link href={`${marchand.getBoutiqueUrl()}/produits`}>
              <Button variant="outline" size="sm" className="flex items-center space-x-2 cursor-pointer">
                <span>{translate('common.see_all_products')}</span>
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {products.map((product) => (
              <CardProduit key={product.id} product={product} />
          ))}
        </div>

        {/* Message si c'est un aperçu avec plus de produits disponibles */}
        {showViewAll && products.length >= 8 && (
          <div className="mt-8 text-center">
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {translate('common.discover_shop', { merchant: marchand.nomEntreprise })}
            </p>
            <Link href={`${marchand.getBoutiqueUrl()}/produits`}>
              <Button className="flex items-center space-x-2 cursor-pointer">
                <span>{translate('common.see_all_products')}</span>
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
