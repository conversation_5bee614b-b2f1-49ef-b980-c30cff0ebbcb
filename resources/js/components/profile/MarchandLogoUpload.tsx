import React from 'react';
import { usePage } from '@inertiajs/react';
import AvatarUpload from '@/components/ui/avatar-upload';
import { avatarService } from '@/services/AvatarService';
import { type SharedData } from '@/types';

interface MarchandLogoUploadProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  onLogoChange?: (logoUrl: string | null) => void;
  marchand?: any; // Optionnel si on veut passer un marchand spécifique
}

export default function MarchandLogoUpload({ 
  className, 
  size = 'lg', 
  disabled = false,
  onLogoChange,
  marchand: propMarchand
}: MarchandLogoUploadProps) {
  const { auth } = usePage<SharedData>().props;
  const user = auth.user;
  
  // Utiliser le marchand passé en prop ou celui de l'utilisateur connecté
  const marchand = propMarchand || user?.marchand;

  if (!marchand) {
    return (
      <div className="text-center text-muted-foreground">
        <p>Aucune boutique associée à ce compte.</p>
      </div>
    );
  }

  const handleUpload = async (file: File) => {
    // Validation côté client
    const validation = avatarService.validateFile(file);
    if (!validation.valid) {
      return {
        success: false,
        message: validation.error || 'Fichier invalide'
      };
    }

    // Optionnel: redimensionner l'image côté client
    const resizedFile = await avatarService.resizeImage(file);
    
    // Upload
    const result = await avatarService.uploadMarchandLogo(resizedFile);
    
    // Notifier le parent du changement
    if (result.success && result.logo_url && onLogoChange) {
      onLogoChange(result.logo_url);
    }
    
    return result;
  };

  const handleDelete = async () => {
    const result = await avatarService.deleteMarchandLogo();
    
    // Notifier le parent du changement
    if (result.success && onLogoChange) {
      onLogoChange(null);
    }
    
    return result;
  };

  // Générer les initiales à partir du nom de l'entreprise
  const fallbackText = avatarService.generateInitials(marchand.nomEntreprise || 'Boutique');

  return (
    <div className="space-y-4">
      <AvatarUpload
        currentImageUrl={marchand.logo}
        fallbackText={fallbackText}
        onUpload={handleUpload}
        onDelete={handleDelete}
        className={className}
        size={size}
        type="logo"
        disabled={disabled}
      />
      
      {/* Informations sur la boutique */}
      <div className="text-center">
        <h3 className="font-semibold text-lg">{marchand.nomEntreprise}</h3>
        {marchand.slug && (
          <p className="text-sm text-muted-foreground">
            Boutique: /{marchand.slug}
          </p>
        )}
      </div>
    </div>
  );
}
