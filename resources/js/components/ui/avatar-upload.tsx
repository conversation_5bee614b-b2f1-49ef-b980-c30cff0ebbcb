import React, { useState, useRef } from 'react';
import { Camera, Upload, X, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface AvatarUploadProps {
  currentImageUrl?: string | null;
  fallbackText?: string;
  onUpload: (file: File) => Promise<{ success: boolean; message: string; avatar_url?: string; logo_url?: string }>;
  onDelete?: () => Promise<{ success: boolean; message: string }>;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  type?: 'avatar' | 'logo';
  disabled?: boolean;
}

const sizeClasses = {
  sm: 'w-16 h-16',
  md: 'w-24 h-24',
  lg: 'w-32 h-32',
  xl: 'w-40 h-40'
};

export default function AvatarUpload({
  currentImageUrl,
  fallbackText = 'U',
  onUpload,
  onDelete,
  className,
  size = 'lg',
  type = 'avatar',
  disabled = false
}: AvatarUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validation du fichier
    if (!file.type.startsWith('image/')) {
      toast({
        title: 'Erreur',
        description: 'Veuillez sélectionner un fichier image.',
        variant: 'destructive',
      });
      return;
    }

    if (file.size > 2 * 1024 * 1024) {
      toast({
        title: 'Erreur',
        description: 'Le fichier ne doit pas dépasser 2MB.',
        variant: 'destructive',
      });
      return;
    }

    // Prévisualisation
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Upload
    handleUpload(file);
  };

  const handleUpload = async (file: File) => {
    setIsUploading(true);
    try {
      const result = await onUpload(file);
      
      if (result.success) {
        toast({
          title: 'Succès',
          description: result.message,
        });
        
        // Mettre à jour l'URL de prévisualisation avec la nouvelle image
        const newImageUrl = result.avatar_url || result.logo_url;
        if (newImageUrl) {
          setPreviewUrl(newImageUrl);
        }
      } else {
        toast({
          title: 'Erreur',
          description: result.message,
          variant: 'destructive',
        });
        setPreviewUrl(null);
      }
    } catch (error) {
      toast({
        title: 'Erreur',
        description: 'Une erreur est survenue lors de l\'upload.',
        variant: 'destructive',
      });
      setPreviewUrl(null);
    } finally {
      setIsUploading(false);
      // Reset input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDelete = async () => {
    if (!onDelete) return;
    
    setIsDeleting(true);
    try {
      const result = await onDelete();
      
      if (result.success) {
        toast({
          title: 'Succès',
          description: result.message,
        });
        setPreviewUrl(null);
      } else {
        toast({
          title: 'Erreur',
          description: result.message,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Erreur',
        description: 'Une erreur est survenue lors de la suppression.',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const triggerFileInput = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  const displayImageUrl = previewUrl || currentImageUrl;
  const isLoading = isUploading || isDeleting;

  return (
    <div className={cn('flex flex-col items-center space-y-4', className)}>
      {/* Avatar/Logo Display */}
      <div className="relative group">
        <Avatar className={cn(sizeClasses[size], 'cursor-pointer transition-all duration-200', {
          'opacity-50': isLoading,
          'hover:opacity-80': !disabled && !isLoading,
          'cursor-not-allowed': disabled
        })}>
          <AvatarImage 
            src={displayImageUrl || undefined} 
            alt={type === 'avatar' ? 'Avatar' : 'Logo'} 
            className="object-cover"
          />
          <AvatarFallback className="bg-muted text-muted-foreground">
            {type === 'logo' ? (
              <div className="text-xs font-semibold">{fallbackText}</div>
            ) : (
              fallbackText
            )}
          </AvatarFallback>
        </Avatar>

        {/* Overlay avec icônes */}
        {!disabled && (
          <div 
            className={cn(
              'absolute inset-0 bg-black/50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer',
              sizeClasses[size]
            )}
            onClick={triggerFileInput}
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
            ) : (
              <Camera className="h-6 w-6 text-white" />
            )}
          </div>
        )}

        {/* Bouton de suppression */}
        {displayImageUrl && onDelete && !disabled && !isLoading && (
          <Button
            variant="destructive"
            size="sm"
            className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
            onClick={handleDelete}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Boutons d'action */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={triggerFileInput}
          disabled={disabled || isLoading}
          className="flex items-center gap-2"
        >
          <Upload className="h-4 w-4" />
          {displayImageUrl ? 'Changer' : 'Ajouter'} {type === 'avatar' ? 'avatar' : 'logo'}
        </Button>

        {displayImageUrl && onDelete && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleDelete}
            disabled={disabled || isLoading}
            className="text-destructive hover:text-destructive"
          >
            Supprimer
          </Button>
        )}
      </div>

      {/* Input file caché */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/png,image/webp"
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />

      {/* Informations */}
      <div className="text-xs text-muted-foreground text-center max-w-xs">
        <p>Formats acceptés: JPEG, PNG, WebP</p>
        <p>Taille maximale: 2MB</p>
        <p>Recommandé: 300x300px</p>
      </div>
    </div>
  );
}
