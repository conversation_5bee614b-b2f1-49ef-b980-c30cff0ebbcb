import { useScrollDirection } from '@/hooks/use-scroll-direction';

/**
 * Composant de test pour vérifier le comportement du scroll
 */
export default function ScrollBehaviorTest() {
  const { scrollDirection, isVisible, scrollY } = useScrollDirection({ threshold: 15 });

  return (
    <div className="fixed top-20 right-4 z-50 bg-background border rounded-lg p-4 shadow-lg text-sm">
      <h3 className="font-semibold mb-2">Scroll Debug</h3>
      <div className="space-y-1">
        <div>Direction: <span className="font-mono">{scrollDirection}</span></div>
        <div>Visible: <span className="font-mono">{isVisible ? 'true' : 'false'}</span></div>
        <div>ScrollY: <span className="font-mono">{Math.round(scrollY)}px</span></div>
      </div>
    </div>
  );
}
