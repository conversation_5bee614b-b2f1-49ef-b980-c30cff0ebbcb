import { useState, useEffect } from 'react';

interface UseScrollDirectionOptions {
  threshold?: number;
  initialDirection?: 'up' | 'down';
}

interface ScrollDirectionState {
  scrollDirection: 'up' | 'down';
  isVisible: boolean;
  scrollY: number;
}

/**
 * Hook personnalisé pour détecter la direction du scroll et gérer la visibilité des éléments
 * 
 * @param options - Options de configuration
 * @returns État du scroll avec direction, visibilité et position Y
 */
export function useScrollDirection(options: UseScrollDirectionOptions = {}): ScrollDirectionState {
  const { threshold = 10, initialDirection = 'up' } = options;
  
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down'>(initialDirection);
  const [isVisible, setIsVisible] = useState(true);
  const [scrollY, setScrollY] = useState(0);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      // Mettre à jour la position Y
      setScrollY(currentScrollY);
      
      // Si on est tout en haut, toujours afficher
      if (currentScrollY <= 0) {
        setIsVisible(true);
        setScrollDirection('up');
        setLastScrollY(currentScrollY);
        return;
      }
      
      // Calculer la différence de scroll
      const scrollDifference = Math.abs(currentScrollY - lastScrollY);
      
      // Ne traiter que si le scroll dépasse le seuil
      if (scrollDifference < threshold) {
        return;
      }
      
      // Déterminer la direction
      const direction = currentScrollY > lastScrollY ? 'down' : 'up';
      
      // Mettre à jour la direction
      setScrollDirection(direction);
      
      // Gérer la visibilité
      if (direction === 'down') {
        setIsVisible(false);
      } else {
        setIsVisible(true);
      }
      
      // Mettre à jour la dernière position
      setLastScrollY(currentScrollY);
    };

    // Ajouter l'écouteur d'événement avec throttling
    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledHandleScroll, { passive: true });
    
    // Appeler une fois au montage pour initialiser
    handleScroll();

    return () => {
      window.removeEventListener('scroll', throttledHandleScroll);
    };
  }, [threshold, lastScrollY]);

  return {
    scrollDirection,
    isVisible,
    scrollY
  };
}
