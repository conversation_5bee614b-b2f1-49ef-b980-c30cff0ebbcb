import axios from 'axios';

/**
 * Interface pour un type de mesure
 */
export interface MeasurementType {
  name_fr: string;
  name_en: string;
  code: string;
  unit: string;
}

/**
 * Interface pour un système de tailles
 */
export interface SizeSystem {
  name_fr: string;
  name_en: string;
  code: string;
}

/**
 * Interface pour une valeur de système
 */
export interface SystemValue {
  system: string;
  value: string;
}

/**
 * Interface pour une mesure
 */
export interface Measurement {
  type: string;
  value: number;
}

/**
 * Interface pour une taille dans le tableau des tailles
 */
export interface  SizeChartEntry {
  size_code: string;
  system_values: SystemValue[];
  measurements: Measurement[];
}

/**
 * Interface pour un guide des tailles
 */
export interface SizeGuide {
  id: number;
  name: string;
  name_fr: string;
  name_en: string;
  category: string;
  measurement_types: MeasurementType[];
  size_systems: SizeSystem[];
  size_chart: SizeChartEntry[];
  instructions: string;
  instructions_fr: string;
  instructions_en: string;
  fitting_tips?: any[];
  image: string | null;
}

/**
 * Interface pour la réponse de l'API pour une catégorie
 */
export interface CategorySizeGuideResponse {
  category: {
    id: number;
    name: string;
  };
  size_guides: SizeGuide[];
}

/**
 * Interface pour la réponse de l'API pour un produit
 */
export interface ProductSizeGuideResponse {
  product: {
    id: number;
    name: string;
    category_id: number;
    category_name: string;
    has_size_variants?: boolean;
  };
  size_guides: SizeGuide[];
}

/**
 * Interface pour la réponse de l'API pour tous les guides de tailles
 */
export interface AllSizeGuidesResponse {
  size_guides: SizeGuide[];
}

/**
 * Service pour gérer les guides des tailles
 */
export class SizeGuideService {
  /**
   * Récupère les guides des tailles pour une catégorie
   *
   * @param categoryId ID de la catégorie
   * @returns Promise avec la réponse de l'API
   */
  static async getForCategory(categoryId: string | number): Promise<CategorySizeGuideResponse> {
    try {
      const response = await axios.get(`/api/size-guides/category/${categoryId}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des guides des tailles pour la catégorie:', error);
      throw error;
    }
  }

  /**
   * Récupère les guides des tailles pour un produit
   *
   * @param productId ID du produit
   * @returns Promise avec la réponse de l'API
   */
  static async getForProduct(productId: string | number): Promise<ProductSizeGuideResponse> {
    try {
      const response = await axios.get(`/api/size-guides/product/${productId}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des guides des tailles pour le produit:', error);
      throw error;
    }
  }

  /**
   * Récupère tous les guides des tailles
   *
   * @returns Promise avec la réponse de l'API
   */
  static async getAllGuides(): Promise<AllSizeGuidesResponse> {
    try {
      const response = await axios.get('/api/size-guides');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération de tous les guides des tailles:', error);
      throw error;
    }
  }

  /**
   * Obtient le nom localisé d'un type de mesure
   *
   * @param measurementTypes Liste des types de mesures
   * @param code Code du type de mesure
   * @param locale Locale actuelle ('fr' ou 'en')
   * @returns Nom localisé du type de mesure
   */
  static getLocalizedMeasurementName(
    measurementTypes: MeasurementType[],
    code: string,
    locale: string = 'fr'
  ): string {
    const measurementType = measurementTypes.find(type => type.code === code);
    if (!measurementType) return code;

    return locale === 'fr' ? measurementType.name_fr : measurementType.name_en;
  }

  /**
   * Obtient le nom localisé d'un système de tailles
   *
   * @param sizeSystems Liste des systèmes de tailles
   * @param code Code du système de tailles
   * @param locale Locale actuelle ('fr' ou 'en')
   * @returns Nom localisé du système de tailles
   */
  static getLocalizedSystemName(
    sizeSystems: SizeSystem[],
    code: string,
    locale: string = 'fr'
  ): string {
    const sizeSystem = sizeSystems.find(system => system.code === code);
    if (!sizeSystem) return code;

    return locale === 'fr' ? sizeSystem.name_fr : sizeSystem.name_en;
  }

  /**
   * Obtient la valeur d'un système pour une taille donnée
   *
   * @param sizeEntry Entrée du tableau des tailles
   * @param systemCode Code du système de tailles
   * @returns Valeur du système pour la taille donnée
   */
  static getSystemValue(sizeEntry: SizeChartEntry, systemCode: string): string {
    // Vérifier si sizeEntry et system_values existent
    if (!sizeEntry || !sizeEntry.system_values || !Array.isArray(sizeEntry.system_values)) {
      return '';
    }

    const systemValue = sizeEntry.system_values.find(sv => sv.system === systemCode);
    return systemValue ? systemValue.value : '';
  }

  /**
   * Obtient la valeur d'une mesure pour une taille donnée
   *
   * @param sizeEntry Entrée du tableau des tailles
   * @param measurementCode Code de la mesure
   * @returns Valeur de la mesure pour la taille donnée
   */
  static getMeasurementValue(sizeEntry: SizeChartEntry, measurementCode: string): number | null {
    // Vérifier si sizeEntry et measurements existent
    if (!sizeEntry || !sizeEntry.measurements || !Array.isArray(sizeEntry.measurements)) {
      return null;
    }

    const measurement = sizeEntry.measurements.find(m => m.type === measurementCode);
    return measurement ? measurement.value : null;
  }
}
