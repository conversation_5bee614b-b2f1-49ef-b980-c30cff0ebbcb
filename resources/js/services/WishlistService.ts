import { Product } from '@/models/Product';

/**
 * Service pour gérer les opérations liées à la liste de souhaits (wishlist)
 */
export class WishlistService {
  private readonly STORAGE_KEY = 'Lorrelei_wishlist';

  /**
   * Récupère tous les produits de la wishlist
   *
   * @returns Un tableau de produits
   */
  getItems(): Product[] {
    try {
      const wishlistData = localStorage.getItem(this.STORAGE_KEY);
      if (!wishlistData) return [];

      const parsedWishlist = JSON.parse(wishlistData);
      return parsedWishlist.map((item: any) =>
        new Product(
          item.id,
          item.name,
          item.slug || '',
          item.description,
          item.price,
          item.discountPrice || null,
          item.discountStartDate ? new Date(item.discountStartDate) : null,
          item.discountEndDate ? new Date(item.discountEndDate) : null,
          item.imageUrl,
          item.imageUrls || [],
          item.category,
          item.inStock,
          item.rating,
          item.reviews,
          item.seller
        )
      );
    } catch (error) {
      console.error('Erreur lors de la récupération de la wishlist:', error);
      return [];
    }
  }

  /**
   * Vérifie si un produit est dans la wishlist
   *
   * @param productId - L'identifiant du produit à vérifier
   * @returns true si le produit est dans la wishlist, false sinon
   */
  isInWishlist(productId: string): boolean {
    const items = this.getItems();
    return items.some(item => item.id === productId);
  }

  /**
   * Ajoute un produit à la wishlist
   *
   * @param product - Le produit à ajouter
   * @returns true si le produit a été ajouté, false s'il était déjà présent
   */
  addItem(product: Product): boolean {
    if (this.isInWishlist(product.id)) {
      return false;
    }

    try {
      const items = this.getItems();
      items.push(product);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(items));
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'ajout à la wishlist:', error);
      return false;
    }
  }

  /**
   * Supprime un produit de la wishlist
   *
   * @param productId - L'identifiant du produit à supprimer
   * @returns true si le produit a été supprimé, false s'il n'était pas présent
   */
  removeItem(productId: string): boolean {
    if (!this.isInWishlist(productId)) {
      return false;
    }

    try {
      const items = this.getItems();
      const updatedItems = items.filter(item => item.id !== productId);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedItems));
      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression de la wishlist:', error);
      return false;
    }
  }

  /**
   * Vide complètement la wishlist
   */
  clearWishlist(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }

  /**
   * Récupère le nombre d'articles dans la wishlist
   *
   * @returns Le nombre d'articles
   */
  getItemCount(): number {
    return this.getItems().length;
  }
}
