import { Marchand } from '@/models/Marchand';

/**
 * Service pour la gestion des marchands
 * 
 * Ce service gère toutes les opérations liées aux marchands :
 * - Récupération des informations des marchands
 * - Récupération des produits d'un marchand
 * - Gestion du cache pour optimiser les performances
 */
export class MarchandService {
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Vérifie si les données en cache sont encore valides
   * 
   * @param key - Clé de cache à vérifier
   * @returns true si les données sont valides, false sinon
   */
  private isCacheValid(key: string): boolean {
    const cached = this.cache.get(key);
    if (!cached) return false;
    
    const now = Date.now();
    return (now - cached.timestamp) < this.CACHE_DURATION;
  }

  /**
   * Récupère les données du cache si elles sont valides
   * 
   * @param key - Clé de cache
   * @returns Les données en cache ou null
   */
  private getCachedData<T>(key: string): T | null {
    if (this.isCacheValid(key)) {
      return this.cache.get(key)!.data as T;
    }
    return null;
  }

  /**
   * Met en cache les données avec un timestamp
   * 
   * @param key - Clé de cache
   * @param data - Données à mettre en cache
   */
  private setCachedData(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Récupère les informations d'un marchand par son ID
   *
   * @param marchandId - ID du marchand
   * @returns Promesse qui résout avec les informations du marchand
   */
  async getMarchandById(marchandId: string): Promise<Marchand | null> {
    try {
      const cacheKey = `/api/marchands/${marchandId}`;

      // Vérifier le cache
      const cachedData = this.getCachedData<Marchand>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Récupérer depuis l'API locale
      const response = await fetch(cacheKey);
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Erreur lors de la récupération du marchand: ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Erreur lors de la récupération du marchand');
      }

      const marchand = this.mapApiMarchandToModel(data.data);

      // Mettre en cache
      this.setCachedData(cacheKey, marchand);

      return marchand;
    } catch (error) {
      console.error('Erreur dans getMarchandById:', error);
      return null;
    }
  }

  /**
   * Récupère les informations d'un marchand par son slug
   *
   * @param slug - Slug du marchand
   * @returns Promesse qui résout avec les informations du marchand
   */
  async getMarchandBySlug(slug: string): Promise<Marchand | null> {
    try {
      const cacheKey = `/api/marchands/slug/${slug}`;

      // Vérifier le cache
      const cachedData = this.getCachedData<Marchand>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Récupérer depuis l'API locale
      const response = await fetch(cacheKey);
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Erreur lors de la récupération du marchand: ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Erreur lors de la récupération du marchand');
      }

      const marchand = this.mapApiMarchandToModel(data.data);

      // Mettre en cache
      this.setCachedData(cacheKey, marchand);

      return marchand;
    } catch (error) {
      console.error('Erreur dans getMarchandBySlug:', error);
      return null;
    }
  }

  /**
   * Récupère les statistiques d'un marchand (nombre de produits, note moyenne, etc.)
   *
   * @param marchandId - ID du marchand
   * @returns Promesse qui résout avec les statistiques du marchand
   */
  async getMarchandStats(marchandId: string): Promise<{
    totalProducts: number;
    averageRating: number;
    totalReviews: number;
    totalSales: number;
    joinedDate: Date;
  } | null> {
    try {
      const cacheKey = `/api/marchands/${marchandId}/stats`;

      // Vérifier le cache
      const cachedData = this.getCachedData<any>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Récupérer depuis l'API locale
      const response = await fetch(cacheKey);
      if (!response.ok) {
        throw new Error(`Erreur lors de la récupération des statistiques: ${response.status}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Erreur lors de la récupération des statistiques');
      }

      const stats = {
        totalProducts: data.data.total_products || 0,
        averageRating: parseFloat(data.data.average_rating) || 0,
        totalReviews: data.data.total_reviews || 0,
        totalSales: data.data.total_sales || 0,
        joinedDate: new Date(data.data.joined_date)
      };

      // Mettre en cache
      this.setCachedData(cacheKey, stats);

      return stats;
    } catch (error) {
      console.error('Erreur dans getMarchandStats:', error);
      return null;
    }
  }

  /**
   * Mappe les données de l'API vers le modèle Marchand
   * 
   * @param apiData - Données brutes de l'API
   * @returns Instance du modèle Marchand
   */
  private mapApiMarchandToModel(apiData: any): Marchand {
    return new Marchand(
      apiData.id?.toString() || '',
      apiData.nomEntreprise || '',
      apiData.slug || '',
      apiData.description_business || '',
      apiData.email_business || '',
      apiData.telephone_principal || '',
      apiData.site_web || '',
      apiData.logo || apiData.logo_url || '',
      apiData.banner_url || '',
      apiData.pays_business || '',
      apiData.ville_business || '',
      apiData.type_business || '',
      parseFloat(apiData.average_rating) || 0,
      parseInt(apiData.reviews_count) || 0,
      apiData.statut_validation === 'approuve',
      new Date(apiData.created_at),
      apiData.categories_produits || []
    );
  }

  /**
   * Vide le cache (utile pour forcer le rechargement des données)
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Vide le cache pour un marchand spécifique
   * 
   * @param marchandId - ID du marchand
   */
  clearMarchandCache(marchandId: string): void {
    const keysToDelete = Array.from(this.cache.keys()).filter(key => 
      key.includes(`/marchands/${marchandId}`)
    );
    
    keysToDelete.forEach(key => this.cache.delete(key));
  }
}

// Instance singleton du service
export const marchandService = new MarchandService();
