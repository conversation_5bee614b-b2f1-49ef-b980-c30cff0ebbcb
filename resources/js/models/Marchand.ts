/**
 * Classe représentant un marchand dans l'application e-commerce
 * 
 * Cette classe encapsule toutes les propriétés et méthodes liées à un marchand
 */
export class Marchand {
  constructor(
    public id: string,
    public nomEntreprise: string,
    public slug: string = '',
    public description: string = '',
    public email: string = '',
    public telephone: string = '',
    public siteWeb: string = '',
    public logoUrl: string = '',
    public bannerUrl: string = '',
    public pays: string = '',
    public ville: string = '',
    public typeBusiness: string = '',
    public averageRating: number = 0,
    public reviewsCount: number = 0,
    public isActive: boolean = true,
    public joinedDate: Date = new Date(),
    public categories: string[] = []
  ) {}

  /**
   * Obtient l'URL de la boutique du marchand
   */
  getBoutiqueUrl(): string {
    return `/boutique/${this.slug}`;
  }

  /**
   * Obtient l'URL du logo du marchand avec fallback
   */
  getLogoUrl(): string {
    return this.logoUrl || '/images/default-merchant-logo.png';
  }

  /**
   * Obtient l'URL de la bannière du marchand avec fallback
   */
  getBannerUrl(): string {
    return this.bannerUrl || '/images/default-merchant-banner.jpg';
  }

  /**
   * Formate la note moyenne du marchand
   */
  getFormattedRating(): string {
    return this.averageRating.toFixed(1);
  }

  /**
   * Obtient le nombre d'étoiles pleines pour l'affichage
   */
  getFullStars(): number {
    return Math.floor(this.averageRating);
  }

  /**
   * Vérifie s'il y a une demi-étoile à afficher
   */
  hasHalfStar(): boolean {
    return (this.averageRating % 1) >= 0.5;
  }

  /**
   * Obtient le nombre d'étoiles vides pour l'affichage
   */
  getEmptyStars(): number {
    const fullStars = this.getFullStars();
    const halfStar = this.hasHalfStar() ? 1 : 0;
    return 5 - fullStars - halfStar;
  }

  /**
   * Formate la date d'inscription du marchand
   */
  getFormattedJoinedDate(): string {
    return this.joinedDate.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long'
    });
  }

  /**
   * Obtient le texte du nombre d'avis
   */
  getReviewsText(): string {
    if (this.reviewsCount === 0) {
      return 'Aucun avis';
    } else if (this.reviewsCount === 1) {
      return '1 avis';
    } else {
      return `${this.reviewsCount} avis`;
    }
  }

  /**
   * Obtient la localisation complète du marchand
   */
  getLocation(): string {
    if (this.ville && this.pays) {
      return `${this.ville}, ${this.pays}`;
    } else if (this.ville) {
      return this.ville;
    } else if (this.pays) {
      return this.pays;
    }
    return '';
  }

  /**
   * Vérifie si le marchand a un site web
   */
  hasSiteWeb(): boolean {
    return this.siteWeb !== '' && this.siteWeb !== null;
  }

  /**
   * Obtient l'URL du site web formatée
   */
  getFormattedSiteWeb(): string {
    if (!this.hasSiteWeb()) return '';
    
    // Ajouter https:// si aucun protocole n'est spécifié
    if (!this.siteWeb.startsWith('http://') && !this.siteWeb.startsWith('https://')) {
      return `https://${this.siteWeb}`;
    }
    
    return this.siteWeb;
  }

  /**
   * Obtient le nom d'affichage du site web (sans protocole)
   */
  getSiteWebDisplayName(): string {
    if (!this.hasSiteWeb()) return '';
    
    return this.siteWeb
      .replace(/^https?:\/\//, '')
      .replace(/^www\./, '')
      .replace(/\/$/, '');
  }

  /**
   * Vérifie si le marchand a une description
   */
  hasDescription(): boolean {
    return this.description !== '' && this.description !== null;
  }

  /**
   * Obtient une version tronquée de la description
   */
  getTruncatedDescription(maxLength: number = 150): string {
    if (!this.hasDescription()) return '';
    
    if (this.description.length <= maxLength) {
      return this.description;
    }
    
    return this.description.substring(0, maxLength).trim() + '...';
  }

  /**
   * Vérifie si le marchand a des catégories de produits
   */
  hasCategories(): boolean {
    return this.categories.length > 0;
  }

  /**
   * Obtient les catégories formatées pour l'affichage
   */
  getFormattedCategories(maxCategories: number = 3): string {
    if (!this.hasCategories()) return '';
    
    const displayCategories = this.categories.slice(0, maxCategories);
    let result = displayCategories.join(', ');
    
    if (this.categories.length > maxCategories) {
      const remaining = this.categories.length - maxCategories;
      result += ` et ${remaining} autre${remaining > 1 ? 's' : ''}`;
    }
    
    return result;
  }

  /**
   * Vérifie si le marchand est bien noté (note >= 4.0)
   */
  isWellRated(): boolean {
    return this.averageRating >= 4.0 && this.reviewsCount >= 5;
  }

  /**
   * Obtient le badge de qualité du marchand
   */
  getQualityBadge(): string | null {
    if (this.averageRating >= 4.8 && this.reviewsCount >= 50) {
      return 'Marchand Excellent';
    } else if (this.averageRating >= 4.5 && this.reviewsCount >= 20) {
      return 'Marchand de Confiance';
    } else if (this.averageRating >= 4.0 && this.reviewsCount >= 10) {
      return 'Marchand Recommandé';
    }
    return null;
  }

  /**
   * Convertit l'instance en objet JSON
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      nomEntreprise: this.nomEntreprise,
      slug: this.slug,
      description: this.description,
      email: this.email,
      telephone: this.telephone,
      siteWeb: this.siteWeb,
      logoUrl: this.logoUrl,
      bannerUrl: this.bannerUrl,
      pays: this.pays,
      ville: this.ville,
      typeBusiness: this.typeBusiness,
      averageRating: this.averageRating,
      reviewsCount: this.reviewsCount,
      isActive: this.isActive,
      joinedDate: this.joinedDate.toISOString(),
      categories: this.categories
    };
  }
}
