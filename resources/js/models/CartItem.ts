import { Product } from './Product';

/**
 * Interface pour les informations de livraison d'un produit
 */
export interface DeliveryInfo {
  frais_livraison: number;
  delai_livraison_min: number;
  delai_livraison_max: number;
  frais_livraison_specifique?: number;
  zone_id?: number;
  zone_nom?: string;
}

/**
 * Classe représentant un élément du panier dans l'application e-commerce
 *
 * Cette classe encapsule un produit, sa quantité et ses informations de livraison
 */
export class CartItem {
  /**
   * Crée une nouvelle instance d'élément de panier
   *
   * @param product - Le produit ajouté au panier
   * @param quantity - La quantité du produit dans le panier
   * @param deliveryInfo - Les informations de livraison pour ce produit (optionnel)
   */
  constructor(
    public product: Product,
    public quantity: number = 1,
    public deliveryInfo?: DeliveryInfo
  ) {}

  /**
   * Calcule le sous-total pour cet élément du panier
   *
   * @returns Le prix total pour cet élément (prix unitaire × quantité)
   */
  getSubtotal(): number {
    // Utiliser getCurrentPrice() pour prendre en compte les remises
    return this.product.getCurrentPrice() * this.quantity;
  }

  /**
   * Calcule les frais de livraison pour cet élément du panier
   *
   * @returns Les frais de livraison pour cet élément
   */
  getDeliveryFees(): number {
    if (!this.deliveryInfo) {
      return 0;
    }


    // Vérifier si les frais de livraison sont définis
    if (this.deliveryInfo.frais_livraison === undefined && this.deliveryInfo.frais_livraison_specifique === undefined) {
      return 0;
    }

    // Utiliser les frais spécifiques s'ils existent, sinon les frais standard
    let fees = 0;
    if (this.deliveryInfo.frais_livraison_specifique !== undefined && this.deliveryInfo.frais_livraison_specifique !== null && this.deliveryInfo.frais_livraison_specifique > 0) {
      fees = this.deliveryInfo.frais_livraison_specifique;
    } else if (this.deliveryInfo.frais_livraison !== undefined && this.deliveryInfo.frais_livraison !== null && this.deliveryInfo.frais_livraison > 0) {
      fees = this.deliveryInfo.frais_livraison;
    } else {
      console.log(`Aucun frais de livraison valide trouvé ou frais égaux à 0`);
    }

    return fees * this.quantity;
  }

  /**
   * Calcule le total pour cet élément du panier, incluant les frais de livraison
   *
   * @returns Le total pour cet élément (sous-total + frais de livraison)
   */
  getTotal(): number {
    return this.getSubtotal() + this.getDeliveryFees();
  }

  /**
   * Formate le sous-total avec le symbole de devise du produit
   *
   * @returns Le sous-total formaté avec le symbole de devise du produit
   */
  formattedSubtotal(): string {
    // Toujours utiliser la devise du produit
    const currency = this.product.currency || 'FCFA';
    return `${this.getSubtotal().toFixed(0)} ${currency}`;
  }

  /**
   * Augmente la quantité de l'élément dans le panier
   *
   * @param amount - Quantité à ajouter (par défaut: 1)
   */
  increaseQuantity(amount: number = 1): void {
    this.quantity += amount;
  }

  /**
   * Diminue la quantité de l'élément dans le panier
   *
   * @param amount - Quantité à retirer (par défaut: 1)
   * @returns false si la quantité devient 0 ou moins, true sinon
   */
  decreaseQuantity(amount: number = 1): boolean {
    this.quantity -= amount;
    if (this.quantity <= 0) {
      this.quantity = 0;
      return false;
    }
    return true;
  }
}
