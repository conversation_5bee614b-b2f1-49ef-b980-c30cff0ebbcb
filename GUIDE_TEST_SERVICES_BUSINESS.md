# Guide de Test - Services Business

## 🎯 Objectif
Ce guide vous permet de tester les nouveaux services business implémentés :
- **StockManagementService** : Gestion automatique des stocks
- **NotificationService** : Notifications en temps réel
- **API de gestion des stocks** : Endpoints pour vérification des stocks
- **Services frontend** : Hooks React et services TypeScript

## 🚀 Services Implémentés

### Backend (Laravel)
- `StockManagementService` : Gestion des stocks lors des commandes
- `NotificationService` : Système de notifications multi-canaux
- `CheckoutService` : Intégration des stocks et notifications
- `StockController` : API REST pour gestion des stocks
- `TestController` : API de test pour validation

### Frontend (React/TypeScript)
- `StockService.ts` : Service pour l'API de stocks
- `useStock.ts` : Hooks React pour gestion des stocks
- `useProductStock` : Hook pour surveiller un produit
- `useCartValidation` : Hook pour validation du panier

## 🧪 Tests Disponibles

### 1. Test du Statut des Services
```bash
GET /api/test/service-status
```
**Objectif** : Vérifier que tous les services sont opérationnels

**Réponse attendue** :
```json
{
  "success": true,
  "message": "Services business opérationnels",
  "services": {
    "StockManagementService": { "status": "active" },
    "NotificationService": { "status": "active" },
    "CheckoutService": { "status": "active" }
  }
}
```

### 2. Test de Vérification des Stocks
```bash
POST /api/test/stock-check
```
**Objectif** : Tester la vérification des stocks pour un panier

**Réponse attendue** :
```json
{
  "success": true,
  "message": "Test de vérification des stocks réussi",
  "data": {
    "cart_tested": [...],
    "stock_check_result": {
      "available": true,
      "unavailable_items": [],
      "warnings": [],
      "total_items_checked": 3
    }
  }
}
```

### 3. Test de Création de Commande
```bash
POST /api/test/order-creation
```
**Objectif** : Tester la création complète d'une commande avec gestion des stocks et notifications

**Réponse attendue** :
```json
{
  "success": true,
  "message": "Test de création de commande réussi",
  "data": {
    "order_result": {
      "commande_principale_id": 123,
      "numero_commande": "CMD-2025-001",
      "nombre_sous_commandes": 2,
      "montant_total": "15000.00"
    }
  }
}
```

### 4. Test d'Annulation de Commande
```bash
POST /api/test/order-cancellation
Content-Type: application/json

{
  "commande_id": 123
}
```
**Objectif** : Tester l'annulation d'une commande et la libération des stocks

### 5. Test du Workflow Complet
```bash
POST /api/test/complete-workflow
Content-Type: application/json

{
  "test_cancellation": true
}
```
**Objectif** : Tester l'ensemble du workflow (stocks → commande → annulation)

## 🔧 API de Gestion des Stocks

### Vérification de Panier
```bash
POST /api/stock/check-availability
Content-Type: application/json

{
  "cart": [
    { "id": 1, "quantity": 2 },
    { "id": 2, "quantity": 1 }
  ]
}
```

### Stock d'un Produit
```bash
GET /api/stock/product/1
```

### Stocks de Plusieurs Produits
```bash
POST /api/stock/multiple-products
Content-Type: application/json

{
  "product_ids": [1, 2, 3]
}
```

## 📋 Procédure de Test Recommandée

### Étape 1 : Vérification des Services
1. Accédez à `/api/test/service-status`
2. Vérifiez que tous les services sont "active"

### Étape 2 : Test des Stocks
1. Exécutez `/api/test/stock-check`
2. Vérifiez que la vérification fonctionne
3. Testez avec `/api/stock/check-availability` manuellement

### Étape 3 : Test de Commande Complète
1. Exécutez `/api/test/order-creation`
2. Notez l'ID de commande créé
3. Vérifiez les logs pour les notifications envoyées

### Étape 4 : Test d'Annulation
1. Utilisez l'ID de commande de l'étape 3
2. Exécutez `/api/test/order-cancellation`
3. Vérifiez que les stocks sont libérés

### Étape 5 : Test Workflow Complet
1. Exécutez `/api/test/complete-workflow`
2. Vérifiez que tous les tests passent

## 🔍 Vérifications dans les Logs

### Logs à Surveiller
- **Stock réservé** : `Stock réservé pour article`
- **Stock libéré** : `Stock libéré pour article`
- **Notifications** : `Email notification`, `Database notification`
- **Commandes** : `Commande créée avec succès`

### Fichiers de Logs
```bash
# Laravel logs
tail -f storage/logs/laravel.log

# Filtrer les logs de stock
tail -f storage/logs/laravel.log | grep -i stock

# Filtrer les logs de notifications
tail -f storage/logs/laravel.log | grep -i notification
```

## ⚠️ Points d'Attention

### Prérequis
1. **Base de données** : Assurez-vous que les migrations sont appliquées
2. **Produits** : Il doit y avoir des produits avec stock > 0
3. **Clients** : Il doit y avoir au moins un client avec une adresse
4. **Marchands** : Les produits doivent appartenir à des marchands

### Données de Test
```sql
-- Vérifier les produits disponibles
SELECT id, nom, stock, statut FROM produits WHERE statut = 'actif' AND stock > 0;

-- Vérifier les clients
SELECT c.id, u.name, u.email FROM clients c JOIN users u ON c.user_id = u.id;

-- Vérifier les adresses
SELECT id, user_id, adresse FROM adresses;
```

## 🐛 Dépannage

### Erreur "Aucun produit disponible"
- Vérifiez qu'il y a des produits avec `statut = 'actif'` et `stock > 0`
- Ajoutez des produits de test si nécessaire

### Erreur "Aucun client trouvé"
- Créez un client de test
- Assurez-vous qu'il a une adresse associée

### Erreur de Service
- Vérifiez que les services sont bien injectés dans les contrôleurs
- Consultez les logs Laravel pour plus de détails

## 📊 Métriques de Succès

### Tests Réussis
- ✅ Statut des services : "active"
- ✅ Vérification des stocks : sans erreurs
- ✅ Création de commande : avec ID généré
- ✅ Notifications : logs présents
- ✅ Annulation : stocks libérés

### Performance
- ⏱️ Vérification des stocks : < 500ms
- ⏱️ Création de commande : < 2s
- ⏱️ Annulation : < 1s

## 🎯 Prochaines Étapes

Après validation des tests :
1. **Intégrer dans le frontend** : Utiliser les hooks React
2. **Tester l'interface utilisateur** : Checkout avec validation des stocks
3. **Optimiser les performances** : Cache et requêtes
4. **Implémenter les notifications temps réel** : WebSockets/Pusher
5. **Créer les services restants** : Support client, pages marchands
