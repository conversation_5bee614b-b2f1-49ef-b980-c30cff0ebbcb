<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Meilisearch Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration pour le moteur de recherche Meilisearch
    |
    */

    'host' => env('MEILISEARCH_HOST', 'http://127.0.0.1:7700'),
    'key' => env('MEILISEARCH_KEY', null),

    /*
    |--------------------------------------------------------------------------
    | Index Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration des index Meilisearch
    |
    */

    'indexes' => [
        'produits' => [
            'name' => env('MEILISEARCH_PRODUITS_INDEX', 'produits'),
            'primary_key' => 'id',
            'searchable_attributes' => [
                'nom',
                'nom_fr',
                'nom_en',
                'description',
                'description_fr',
                'description_en',
                'marque',
                'product_code',
                'categorie_nom',
                'marchand_nom'
            ],
            'filterable_attributes' => [
                'categorie_id',
                'marchand_id',
                'prix',
                'stock',
                'discount_price',
                'is_on_discount',
                'average_rating',
                'created_at'
            ],
            'sortable_attributes' => [
                'prix',
                'created_at',
                'average_rating',
                'reviews_count',
                'nom'
            ],
            'ranking_rules' => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'sort',
                'exactness'
            ],
            'stop_words' => ['le', 'la', 'les', 'de', 'du', 'des', 'et', 'ou', 'un', 'une'],
            'synonyms' => [
                'telephone' => ['tel', 'phone', 'mobile'],
                'ordinateur' => ['pc', 'computer', 'laptop'],
                'vetement' => ['habit', 'clothing', 'clothes']
            ]
        ],

        'categories' => [
            'name' => env('MEILISEARCH_CATEGORIES_INDEX', 'categories'),
            'primary_key' => 'id',
            'searchable_attributes' => [
                'nom',
                'description',
                'category_path'
            ],
            'filterable_attributes' => [
                'categorie_parent_id',
                'niveau',
                'has_products'
            ],
            'sortable_attributes' => [
                'nom',
                'niveau',
                'created_at'
            ],
            'ranking_rules' => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'sort',
                'exactness'
            ]
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Search Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration par défaut pour les recherches
    |
    */

    'search' => [
        'limit' => env('MEILISEARCH_SEARCH_LIMIT', 20),
        'offset' => 0,
        'highlight_pre_tag' => '<mark>',
        'highlight_post_tag' => '</mark>',
        'crop_length' => 200,
        'crop_marker' => '...',
        'matches_threshold' => 1,
        'facets_distribution' => true
    ],

    /*
    |--------------------------------------------------------------------------
    | Fallback Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration pour le fallback vers la base de données
    |
    */

    'fallback' => [
        'enabled' => env('MEILISEARCH_FALLBACK_ENABLED', true),
        'timeout' => env('MEILISEARCH_TIMEOUT', 5), // secondes
        'retry_attempts' => env('MEILISEARCH_RETRY_ATTEMPTS', 3)
    ]
];
