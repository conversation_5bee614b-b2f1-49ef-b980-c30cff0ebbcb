#!/bin/bash

# 🚀 Script d'Installation WebSocket - Laravel Reverb
# Installe les dépendances nécessaires pour les WebSockets

echo "🔄 Installation des dépendances WebSocket..."

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# Fonction pour vérifier si une commande existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Vérifier que npm est installé
if ! command_exists npm; then
    echo -e "${RED}❌ npm n'est pas installé. Veuillez installer Node.js d'abord.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ npm trouvé${NC}"

# Installation côté client (lorrelei)
echo -e "\n${YELLOW}📱 Installation dépendances côté CLIENT...${NC}"
if [ -d "lorrelei" ]; then
    cd lorrelei
    
    echo -e "${CYAN}📦 Installation laravel-echo et pusher-js...${NC}"
    npm install laravel-echo pusher-js
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Dépendances client installées avec succès${NC}"
    else
        echo -e "${RED}❌ Erreur lors de l'installation côté client${NC}"
    fi
    
    cd ..
else
    echo -e "${YELLOW}⚠️ Dossier lorrelei/ non trouvé${NC}"
fi

# Installation côté admin (admin_marchand_lorrelei)
echo -e "\n${YELLOW}🛠️ Installation dépendances côté ADMIN...${NC}"
if [ -d "admin_marchand_lorrelei" ]; then
    cd admin_marchand_lorrelei
    
    echo -e "${CYAN}📦 Installation laravel-echo et pusher-js...${NC}"
    npm install laravel-echo pusher-js
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Dépendances admin installées avec succès${NC}"
    else
        echo -e "${RED}❌ Erreur lors de l'installation côté admin${NC}"
    fi
    
    cd ..
else
    echo -e "${YELLOW}⚠️ Dossier admin_marchand_lorrelei/ non trouvé${NC}"
fi

# Vérification des variables d'environnement
echo -e "\n${YELLOW}🔧 Vérification de la configuration...${NC}"

env_files=("lorrelei/.env" "admin_marchand_lorrelei/.env")
required_vars=("VITE_REVERB_APP_KEY" "VITE_REVERB_HOST" "VITE_REVERB_PORT" "VITE_REVERB_SCHEME")

for env_file in "${env_files[@]}"; do
    if [ -f "$env_file" ]; then
        echo -e "${CYAN}📄 Vérification de $env_file...${NC}"
        
        missing_vars=()
        for var in "${required_vars[@]}"; do
            if ! grep -q "^$var=" "$env_file"; then
                missing_vars+=("$var")
            fi
        done
        
        if [ ${#missing_vars[@]} -eq 0 ]; then
            echo -e "${GREEN}✅ Toutes les variables Reverb sont configurées${NC}"
        else
            echo -e "${YELLOW}⚠️ Variables manquantes dans $env_file :${NC}"
            for missing in "${missing_vars[@]}"; do
                echo -e "${RED}   - $missing${NC}"
            done
            
            echo -e "\n${CYAN}💡 Ajoutez ces variables à $env_file :${NC}"
            echo -e "${GRAY}VITE_REVERB_APP_KEY=local-key${NC}"
            echo -e "${GRAY}VITE_REVERB_HOST=localhost${NC}"
            echo -e "${GRAY}VITE_REVERB_PORT=8080${NC}"
            echo -e "${GRAY}VITE_REVERB_SCHEME=http${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ Fichier $env_file non trouvé${NC}"
    fi
done

# Instructions finales
echo -e "\n${CYAN}🎯 PROCHAINES ÉTAPES :${NC}"
echo -e "${WHITE}1. 🔧 Vérifiez que Laravel Reverb est configuré côté backend${NC}"
echo -e "${WHITE}2. 🚀 Démarrez le serveur Reverb : php artisan reverb:start${NC}"
echo -e "${WHITE}3. 🌐 Démarrez les serveurs de développement :${NC}"
echo -e "${GRAY}   - Client : cd lorrelei && npm run dev${NC}"
echo -e "${GRAY}   - Admin : cd admin_marchand_lorrelei && npm run dev${NC}"
echo -e "${WHITE}4. 🧪 Testez les WebSockets selon le guide WEBSOCKET_TESTING_GUIDE.md${NC}"

echo -e "\n${CYAN}📚 DOCUMENTATION :${NC}"
echo -e "${WHITE}- 📄 WEBSOCKET_IMPLEMENTATION.md - Détails de l'implémentation${NC}"
echo -e "${WHITE}- 🧪 WEBSOCKET_TESTING_GUIDE.md - Guide de test complet${NC}"

echo -e "\n${GREEN}✨ Installation terminée ! Les WebSockets sont prêts à être testés.${NC}"
