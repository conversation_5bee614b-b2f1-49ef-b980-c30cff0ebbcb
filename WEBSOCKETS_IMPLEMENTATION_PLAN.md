# 🚀 PLAN D'IMPLÉMENTATION WEBSOCKETS - MESSAGES INSTANTANÉS

## 📋 PHASE 1 : CONFIGURATION DE BASE

### 1.1 Configuration Laravel Broadcasting

```php
// admin_marchand_lorrelei/config/broadcasting.php
'pusher' => [
    'driver' => 'pusher',
    'key' => env('PUSHER_APP_KEY'),
    'secret' => env('PUSHER_APP_SECRET'),
    'app_id' => env('PUSHER_APP_ID'),
    'options' => [
        'cluster' => env('PUSHER_APP_CLUSTER'),
        'useTLS' => true,
        'host' => env('PUSHER_HOST', '127.0.0.1'),
        'port' => env('PUSHER_PORT', 6001),
        'scheme' => env('PUSHER_SCHEME', 'http'),
    ],
],
```

### 1.2 Variables d'Environnement

```bash
# admin_marchand_lorrelei/.env
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=local-app
PUSHER_APP_KEY=local-key
PUSHER_APP_SECRET=local-secret
PUSHER_APP_CLUSTER=mt1
PUSHER_HOST=127.0.0.1
PUSHER_PORT=6001
PUSHER_SCHEME=http

# lorrelei/.env
VITE_PUSHER_APP_KEY=local-key
VITE_PUSHER_HOST=127.0.0.1
VITE_PUSHER_PORT=6001
VITE_PUSHER_SCHEME=http
VITE_PUSHER_APP_CLUSTER=mt1
```

### 1.3 Installation Laravel WebSockets

```bash
# admin_marchand_lorrelei
composer require beyondcode/laravel-websockets
php artisan vendor:publish --provider="BeyondCode\LaravelWebSockets\WebSocketsServiceProvider" --tag="migrations"
php artisan migrate
php artisan vendor:publish --provider="BeyondCode\LaravelWebSockets\WebSocketsServiceProvider" --tag="config"
```

## 📦 PHASE 2 : ÉVÉNEMENTS ET CANAUX

### 2.1 Événements de Messages

```php
// admin_marchand_lorrelei/app/Events/MessageSent.php
<?php

namespace App\Events;

use App\Models\ClientMarchandMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $message;
    public $conversation;

    public function __construct(ClientMarchandMessage $message)
    {
        $this->message = $message->load(['auteur']);
        $this->conversation = $message->conversation;
    }

    public function broadcastOn()
    {
        return [
            new PrivateChannel('conversation.' . $this->conversation->id),
            new PrivateChannel('user.' . $this->conversation->client_id),
            new PrivateChannel('marchand.' . $this->conversation->marchand_id),
        ];
    }

    public function broadcastAs()
    {
        return 'message.sent';
    }

    public function broadcastWith()
    {
        return [
            'message' => [
                'id' => $this->message->id,
                'message' => $this->message->message,
                'auteur_type' => $this->message->auteur_type,
                'auteur_nom' => $this->message->auteur_nom,
                'created_at' => $this->message->created_at->toISOString(),
                'pieces_jointes' => $this->message->pieces_jointes,
            ],
            'conversation' => [
                'id' => $this->conversation->id,
                'sujet' => $this->conversation->sujet,
                'statut' => $this->conversation->statut,
                'date_dernier_message' => $this->conversation->date_dernier_message,
            ],
        ];
    }
}
```

### 2.2 Événements de Litiges

```php
// admin_marchand_lorrelei/app/Events/DisputeMessageSent.php
<?php

namespace App\Events;

use App\Models\DisputeMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class DisputeMessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $message;
    public $dispute;

    public function __construct(DisputeMessage $message)
    {
        $this->message = $message;
        $this->dispute = $message->dispute;
    }

    public function broadcastOn()
    {
        return [
            new PrivateChannel('dispute.' . $this->dispute->id),
            new PrivateChannel('user.' . $this->dispute->client_id),
            new PrivateChannel('marchand.' . $this->dispute->marchand_id),
            new PrivateChannel('admin.disputes'), // Canal admin global
        ];
    }

    public function broadcastAs()
    {
        return 'dispute.message.sent';
    }

    public function broadcastWith()
    {
        return [
            'message' => [
                'id' => $this->message->id,
                'message' => $this->message->message,
                'auteur_type' => $this->message->auteur_type,
                'auteur_nom' => $this->message->auteur_nom,
                'type_message' => $this->message->type_message,
                'created_at' => $this->message->created_at->toISOString(),
                'pieces_jointes' => $this->message->pieces_jointes,
                'interne' => $this->message->interne,
            ],
            'dispute' => [
                'id' => $this->dispute->id,
                'numero_litige' => $this->dispute->numero_litige,
                'sujet' => $this->dispute->sujet,
                'statut' => $this->dispute->statut,
                'priorite' => $this->dispute->priorite,
            ],
        ];
    }
}
```

### 2.3 Événements de Présence

```php
// admin_marchand_lorrelei/app/Events/UserTyping.php
<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserTyping implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $userId;
    public $userName;
    public $conversationId;
    public $isTyping;

    public function __construct($userId, $userName, $conversationId, $isTyping = true)
    {
        $this->userId = $userId;
        $this->userName = $userName;
        $this->conversationId = $conversationId;
        $this->isTyping = $isTyping;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('conversation.' . $this->conversationId);
    }

    public function broadcastAs()
    {
        return 'user.typing';
    }

    public function broadcastWith()
    {
        return [
            'user_id' => $this->userId,
            'user_name' => $this->userName,
            'is_typing' => $this->isTyping,
        ];
    }
}
```

## 📦 PHASE 3 : SERVICES WEBSOCKETS

### 3.1 Service WebSocket Backend

```php
// admin_marchand_lorrelei/app/Services/WebSocketService.php
<?php

namespace App\Services;

use App\Events\MessageSent;
use App\Events\DisputeMessageSent;
use App\Events\UserTyping;
use App\Models\ClientMarchandMessage;
use App\Models\DisputeMessage;
use Illuminate\Support\Facades\Auth;

class WebSocketService
{
    /**
     * Diffuser un nouveau message de conversation
     */
    public function broadcastMessage(ClientMarchandMessage $message): void
    {
        broadcast(new MessageSent($message))->toOthers();
    }

    /**
     * Diffuser un nouveau message de litige
     */
    public function broadcastDisputeMessage(DisputeMessage $message): void
    {
        broadcast(new DisputeMessageSent($message))->toOthers();
    }

    /**
     * Diffuser l'état "en train d'écrire"
     */
    public function broadcastTyping(string $conversationId, bool $isTyping = true): void
    {
        $user = Auth::user();
        
        broadcast(new UserTyping(
            $user->id,
            $user->name ?? 'Utilisateur',
            $conversationId,
            $isTyping
        ))->toOthers();
    }

    /**
     * Marquer les messages comme lus et diffuser
     */
    public function markAsRead(string $conversationId, string $userType): void
    {
        // Logique pour marquer comme lu
        // Diffuser l'événement de lecture
        
        broadcast(new \App\Events\MessagesRead([
            'conversation_id' => $conversationId,
            'user_type' => $userType,
            'user_id' => Auth::id(),
            'read_at' => now()->toISOString(),
        ]))->toOthers();
    }
}
```

## 🎯 PLANNING D'IMPLÉMENTATION

### Semaine 1 : Configuration de Base
- ✅ Installation et configuration Laravel WebSockets
- ✅ Configuration des canaux et événements
- ✅ Tests de connexion WebSocket

### Semaine 2 : Backend Events
- ✅ Création des événements (MessageSent, DisputeMessageSent, UserTyping)
- ✅ Service WebSocketService
- ✅ Intégration dans les contrôleurs existants

### Semaine 3 : Frontend React
- ✅ Service WebSocket frontend
- ✅ Hook useWebSocket
- ✅ Composants Chat avec temps réel

### Semaine 4 : Tests et Optimisations
- ✅ Tests end-to-end
- ✅ Optimisations performance
- ✅ Monitoring et logs
- ✅ Déploiement production

## 🚀 RÉSULTAT ATTENDU

Après implémentation :
- ✅ **Messages instantanés** sans rechargement
- ✅ **Notifications temps réel** pour tous les acteurs
- ✅ **Indicateurs de présence** (en ligne, en train d'écrire)
- ✅ **Expérience utilisateur moderne** type WhatsApp/Telegram
- ✅ **Synchronisation parfaite** entre client/admin/marchand
