<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Marchand;
use App\Models\MarchandAbonnement;
use App\Models\MarchandAbonnementHistorique;
use Illuminate\Support\Facades\Hash;

class MarchandSellerPlatformSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un utilisateur admin pour la validation
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'password' => Hash::make('password'),
                'role' => 'Admin',
                'is_active' => true,
            ]
        );

        // Créer des utilisateurs marchands de test
        $this->createTestMarchands($admin);

        // Créer des exemples d'abonnements
        $this->createTestAbonnements();

        $this->command->info('Données de test pour la plateforme seller créées avec succès !');
    }

    /**
     * Crée des marchands de test avec différents statuts
     */
    private function createTestMarchands(User $admin): void
    {
        // Marchand validé avec abonnement Premium
        $userPremium = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'password' => Hash::make('password'),
                'role' => 'Marchand',
                'is_active' => true,
            ]
        );

        $marchandPremium = Marchand::firstOrCreate(
            ['user_id' => $userPremium->id],
            [
                'nomEntreprise' => 'Boutique Premium SARL',
                'pays_business' => 'Cameroun',
                'ville_business' => 'Douala',
                'type_business' => 'entreprise',
                'statut_validation' => 'valide',
                'etape_inscription' => 'termine',
                'date_validation' => now()->subDays(30),
                'validateur_id' => $admin->id,
                'telephone_principal' => '+237123456789',
                'email_business' => '<EMAIL>',
                'methode_paiement_preferee' => 'bancaire',
                'description_business' => 'Boutique spécialisée dans la vente de produits électroniques et accessoires.',
                'chiffre_affaires_estime' => 5000000.00,
                'nombre_employes' => 15,
                'accepte_conditions' => true,
                'accepte_newsletter' => true,
                'langue_preferee' => 'fr',
                'source_inscription' => 'publicite_facebook',
            ]
        );

        // Marchand en cours de validation
        $userEnCours = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'password' => Hash::make('password'),
                'role' => 'Marchand',
                'is_active' => true,
            ]
        );

        Marchand::firstOrCreate(
            ['user_id' => $userEnCours->id],
            [
                'nomEntreprise' => 'Artisan Local',
                'pays_business' => 'Côte d\'Ivoire',
                'ville_business' => 'Abidjan',
                'type_business' => 'individuel',
                'statut_validation' => 'en_verification',
                'etape_inscription' => 'verification',
                'date_soumission_documents' => now()->subDays(2),
                'telephone_principal' => '+225123456789',
                'email_business' => '<EMAIL>',
                'methode_paiement_preferee' => 'orange_money',
                'numero_orange_money' => '+225123456789',
                'description_business' => 'Artisan spécialisé dans la fabrication de produits traditionnels.',
                'chiffre_affaires_estime' => 500000.00,
                'nombre_employes' => 2,
                'accepte_conditions' => true,
                'accepte_newsletter' => true,
                'langue_preferee' => 'fr',
                'source_inscription' => 'recommandation',
            ]
        );

        // Marchand débutant (gratuit)
        $userGratuit = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'password' => Hash::make('password'),
                'role' => 'Marchand',
                'is_active' => true,
            ]
        );

        $marchandGratuit = Marchand::firstOrCreate(
            ['user_id' => $userGratuit->id],
            [
                'nomEntreprise' => 'Petit Commerce',
                'pays_business' => 'Sénégal',
                'ville_business' => 'Dakar',
                'type_business' => 'individuel',
                'statut_validation' => 'valide',
                'etape_inscription' => 'termine',
                'date_validation' => now()->subDays(7),
                'validateur_id' => $admin->id,
                'telephone_principal' => '+221123456789',
                'email_business' => '<EMAIL>',
                'methode_paiement_preferee' => 'mtn_money',
                'numero_mtn_money' => '+221123456789',
                'description_business' => 'Petit commerce de proximité.',
                'chiffre_affaires_estime' => 100000.00,
                'nombre_employes' => 1,
                'accepte_conditions' => true,
                'accepte_newsletter' => false,
                'langue_preferee' => 'fr',
                'source_inscription' => 'recherche_google',
            ]
        );

        // Marchand Elite
        $userElite = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'password' => Hash::make('password'),
                'role' => 'Marchand',
                'is_active' => true,
            ]
        );

        $marchandElite = Marchand::firstOrCreate(
            ['user_id' => $userElite->id],
            [
                'nomEntreprise' => 'Grande Distribution SA',
                'pays_business' => 'Cameroun',
                'ville_business' => 'Yaoundé',
                'type_business' => 'grande_entreprise',
                'statut_validation' => 'valide',
                'etape_inscription' => 'termine',
                'date_validation' => now()->subDays(90),
                'validateur_id' => $admin->id,
                'telephone_principal' => '+237987654321',
                'telephone_secondaire' => '+237123987654',
                'email_business' => '<EMAIL>',
                'site_web' => 'https://www.grande-distribution.cm',
                'methode_paiement_preferee' => 'bancaire',
                'description_business' => 'Grande entreprise de distribution avec plusieurs points de vente.',
                'chiffre_affaires_estime' => 50000000.00,
                'nombre_employes' => 200,
                'accepte_conditions' => true,
                'accepte_newsletter' => true,
                'langue_preferee' => 'fr',
                'source_inscription' => 'partenariat_commercial',
                'parrain_id' => $marchandPremium->id,
            ]
        );

        $this->command->info('4 marchands de test créés avec différents statuts.');
    }

    /**
     * Crée des abonnements de test
     */
    private function createTestAbonnements(): void
    {
        $marchands = Marchand::whereIn('statut_validation', ['valide'])->get();

        foreach ($marchands as $marchand) {
            // Déterminer le type d'abonnement selon le marchand
            $typeAbonnement = match ($marchand->nomEntreprise) {
                'Boutique Premium SARL' => 'premium',
                'Grande Distribution SA' => 'elite',
                'Petit Commerce' => 'gratuit',
                default => 'gratuit'
            };

            // Créer l'abonnement
            $abonnement = MarchandAbonnement::creerAbonnement(
                $marchand->id,
                $typeAbonnement,
                [
                    'date_debut' => now()->subDays(30),
                    'est_periode_essai' => $typeAbonnement !== 'gratuit',
                    'fin_periode_essai' => $typeAbonnement !== 'gratuit' ? now()->subDays(16) : null,
                ]
            );

            // Créer l'historique de création
            MarchandAbonnementHistorique::enregistrerAction(
                $marchand->id,
                'creation',
                [
                    'abonnement_id' => $abonnement->id,
                    'type_abonnement_apres' => $typeAbonnement,
                    'statut_apres' => 'actif',
                    'prix_apres' => $abonnement->prix_mensuel,
                    'date_debut_periode' => $abonnement->date_debut,
                    'date_fin_periode' => $abonnement->date_fin,
                    'periode_essai_utilisee' => $abonnement->est_periode_essai,
                    'initie_par' => 'marchand',
                    'user_id' => $marchand->user_id,
                ]
            );

            // Si ce n'est pas gratuit, ajouter quelques paiements
            if ($typeAbonnement !== 'gratuit') {
                MarchandAbonnementHistorique::enregistrerAction(
                    $marchand->id,
                    'renouvellement',
                    [
                        'abonnement_id' => $abonnement->id,
                        'type_abonnement_apres' => $typeAbonnement,
                        'statut_apres' => 'actif',
                        'prix_apres' => $abonnement->prix_mensuel,
                        'montant_paye' => $abonnement->prix_mensuel,
                        'reference_paiement' => 'PAY_' . strtoupper(uniqid()),
                        'methode_paiement' => 'carte_bancaire',
                        'initie_par' => 'marchand',
                        'user_id' => $marchand->user_id,
                    ]
                );
            }
        }

        $this->command->info('Abonnements de test créés pour les marchands validés.');
    }
}
