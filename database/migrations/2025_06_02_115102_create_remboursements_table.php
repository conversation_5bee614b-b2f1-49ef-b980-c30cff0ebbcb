<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('remboursements', function (Blueprint $table) {
            $table->id();
            
            // Relations principales
            $table->foreignId('commande_principale_id')->constrained('commandes_principales')->onDelete('cascade');
            $table->foreignId('sous_commande_id')->nullable()->constrained('sous_commandes_vendeur')->onDelete('cascade');
            $table->foreignId('client_id')->constrained('clients')->onDelete('cascade');
            $table->foreignId('marchand_id')->nullable()->constrained('marchands')->onDelete('cascade');
            
            // Référence unique du remboursement
            $table->string('reference_remboursement', 100)->unique();
            
            // Type de remboursement
            $table->enum('type_remboursement', [
                'total',               // Remboursement total de la commande
                'partiel',             // Remboursement partiel
                'article',             // Remboursement d'un article spécifique
                'frais_livraison',     // Remboursement des frais de livraison uniquement
                'compensation'         // Compensation (sans retour physique)
            ]);
            
            // Motif du remboursement
            $table->enum('motif', [
                'defaut_produit',      // Défaut ou dommage du produit
                'non_conforme',        // Produit non conforme à la description
                'livraison_retard',    // Retard de livraison
                'erreur_commande',     // Erreur dans la commande
                'annulation_client',   // Annulation par le client
                'annulation_marchand', // Annulation par le marchand
                'produit_manquant',    // Produit manquant dans le colis
                'erreur_prix',         // Erreur de prix
                'autre'                // Autre motif
            ]);
            
            // Montants
            $table->decimal('montant_demande', 10, 2)->comment('Montant demandé en remboursement');
            $table->decimal('montant_approuve', 10, 2)->nullable()->comment('Montant approuvé pour remboursement');
            $table->decimal('montant_rembourse', 10, 2)->nullable()->comment('Montant effectivement remboursé');
            $table->decimal('frais_retour', 8, 2)->default(0)->comment('Frais de retour à déduire');
            $table->string('devise', 10)->default('FCFA');
            
            // Statut du remboursement
            $table->enum('statut', [
                'demande',             // Demande initiale
                'en_attente_retour',   // En attente du retour du produit
                'retour_recu',         // Retour reçu par le marchand
                'en_evaluation',       // En cours d'évaluation
                'approuve',            // Remboursement approuvé
                'refuse',              // Remboursement refusé
                'en_cours',            // Remboursement en cours de traitement
                'complete',            // Remboursement effectué
                'echec',               // Échec du remboursement
                'litige'               // En litige
            ])->default('demande');
            
            // Informations sur le retour
            $table->boolean('retour_physique_requis')->default(true);
            $table->string('transporteur_retour', 100)->nullable();
            $table->string('numero_suivi_retour', 100)->nullable();
            $table->timestamp('date_retour_expedie')->nullable();
            $table->timestamp('date_retour_recu')->nullable();
            
            // Descriptions et justifications
            $table->text('description_probleme')->comment('Description détaillée du problème');
            $table->text('justification_client')->nullable()->comment('Justification fournie par le client');
            $table->text('evaluation_marchand')->nullable()->comment('Évaluation du marchand');
            $table->text('decision_admin')->nullable()->comment('Décision de l\'administrateur');
            $table->text('motif_refus')->nullable()->comment('Motif en cas de refus');
            
            // Preuves et documents
            $table->json('preuves_client')->nullable()->comment('Photos/documents fournis par le client');
            $table->json('preuves_marchand')->nullable()->comment('Photos/documents fournis par le marchand');
            
            // Dates importantes
            $table->timestamp('date_demande')->useCurrent();
            $table->timestamp('date_evaluation')->nullable();
            $table->timestamp('date_decision')->nullable();
            $table->timestamp('date_remboursement')->nullable();
            $table->integer('delai_retour_jours')->default(14)->comment('Délai accordé pour le retour en jours');
            
            // Responsables du traitement
            $table->foreignId('traite_par_marchand')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('traite_par_admin')->nullable()->constrained('users')->onDelete('set null');
            
            // Informations de transaction
            $table->string('reference_transaction_remboursement', 255)->nullable();
            $table->string('methode_remboursement', 50)->nullable()->comment('Méthode utilisée pour le remboursement');
            
            // Gestion des litiges
            $table->boolean('en_litige')->default(false);
            $table->timestamp('date_ouverture_litige')->nullable();
            $table->text('details_litige')->nullable();
            
            // Métadonnées
            $table->json('metadata')->nullable()->comment('Données supplémentaires');
            
            $table->timestamps();
            
            // Index pour optimiser les performances
            $table->index(['commande_principale_id', 'statut']);
            $table->index(['client_id', 'statut']);
            $table->index(['marchand_id', 'statut']);
            $table->index(['statut', 'date_demande']);
            $table->index(['type_remboursement', 'motif']);
            $table->index(['en_litige']);
            $table->index(['retour_physique_requis', 'statut']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('remboursements');
    }
};
