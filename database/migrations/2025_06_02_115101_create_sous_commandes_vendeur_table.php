<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sous_commandes_vendeur', function (Blueprint $table) {
            $table->id();

            // Relations principales
            $table->foreignId('commande_principale_id')->constrained('commandes_principales')->onDelete('cascade');
            $table->foreignId('marchand_id')->constrained('marchands')->onDelete('cascade');

            // Numéro de sous-commande unique
            $table->string('numero_sous_commande', 50)->unique();

            // Montants financiers pour ce marchand
            $table->decimal('montant_ht', 10, 2)->comment('Montant hors taxes pour ce marchand');
            $table->decimal('montant_ttc', 10, 2)->comment('Montant TTC pour ce marchand');
            $table->decimal('montant_taxes', 10, 2)->default(0)->comment('Montant des taxes pour ce marchand');
            $table->decimal('montant_commission', 10, 2)->default(0)->comment('Commission prélevée par la plateforme');
            $table->decimal('montant_versement_marchand', 10, 2)->comment('Montant net à verser au marchand');
            $table->decimal('taux_commission', 5, 2)->default(0)->comment('Taux de commission appliqué (en %)');

            // Statut spécifique à cette sous-commande
            $table->enum('statut', [
                'EnAttente',           // En attente de traitement par le marchand
                'Confirmé',            // Confirmé par le marchand
                'EnPreparation',       // En cours de préparation
                'PrêtExpédition',      // Prêt pour expédition
                'Expédié',             // Expédié par le marchand
                'EnTransit',           // En transit
                'Livré',               // Livré au client
                'Annulé',              // Annulé par le marchand ou le client
                'Retourné',            // Retourné par le client
                'Remboursé'            // Remboursé
            ])->default('EnAttente');

            // Informations de livraison
            $table->decimal('frais_livraison', 8, 2)->default(0)->comment('Frais de livraison pour ce marchand');
            $table->string('transporteur', 100)->nullable()->comment('Nom du transporteur');
            $table->string('numero_suivi', 100)->nullable()->comment('Numéro de suivi du colis');
            $table->string('url_suivi', 255)->nullable()->comment('URL de suivi du transporteur');

            // Dates importantes
            $table->timestamp('date_creation')->useCurrent();
            $table->timestamp('date_confirmation_marchand')->nullable();
            $table->date('date_expedition_prevue')->nullable();
            $table->timestamp('date_expedition_reelle')->nullable();
            $table->date('date_livraison_prevue')->nullable();
            $table->timestamp('date_livraison_reelle')->nullable();
            $table->integer('delai_preparation_jours')->nullable()->comment('Délai de préparation estimé en jours');

            // Informations de contact et communication
            $table->text('instructions_marchand')->nullable()->comment('Instructions spéciales du marchand');
            $table->text('message_client')->nullable()->comment('Message du marchand au client');
            $table->text('notes_internes')->nullable()->comment('Notes internes du marchand');

            // Informations sur les articles
            $table->integer('nombre_articles')->default(0)->comment('Nombre d\'articles dans cette sous-commande');
            $table->decimal('poids_total', 8, 2)->nullable()->comment('Poids total des articles en kg');

            // Informations de versement (référence vers table versements qui sera dans admin_marchand_lorrelei)
            $table->boolean('versement_effectué')->default(false);
            $table->timestamp('date_versement')->nullable();
            $table->unsignedBigInteger('versement_id')->nullable()->comment('Référence vers table versements');

            // Zone de livraison (utilise la table existante)
            $table->foreignId('zone_livraison_id')->nullable()->constrained('zones_livraison')->onDelete('set null');

            // Métadonnées
            $table->json('metadata')->nullable()->comment('Données supplémentaires spécifiques au marchand');

            $table->timestamps();

            // Index pour optimiser les performances
            $table->index(['commande_principale_id', 'marchand_id']);
            $table->index(['marchand_id', 'statut']);
            $table->index(['statut', 'date_creation']);
            $table->index(['numero_sous_commande']);
            $table->index(['date_expedition_prevue']);
            $table->index(['versement_effectué', 'marchand_id']);

            // Contrainte unique pour éviter les doublons
            $table->unique(['commande_principale_id', 'marchand_id'], 'unique_commande_marchand');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sous_commandes_vendeur');
    }
};
