<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('article_commandes', function (Blueprint $table) {
            // Rendre commande_id nullable pour le système multi-vendor
            $table->foreignId('commande_id')->nullable()->change();

            // Ajouter la relation vers la sous-commande vendeur
            $table->foreignId('sous_commande_id')->nullable()->after('commande_id')
                ->constrained('sous_commandes_vendeur')->onDelete('cascade');

            // Ajouter les prix HT et TTC
            $table->decimal('prix_unitaire_ht', 10, 2)->nullable()->after('prixUnitaire')
                ->comment('Prix unitaire hors taxes');

            $table->decimal('prix_unitaire_ttc', 10, 2)->nullable()->after('prix_unitaire_ht')
                ->comment('Prix unitaire toutes taxes comprises');

            // Ajouter le taux de TVA
            $table->decimal('taux_tva', 5, 2)->default(0)->after('prix_unitaire_ttc')
                ->comment('Taux de TVA appliqué (en %)');

            // Ajouter le montant de TVA
            $table->decimal('montant_tva', 10, 2)->default(0)->after('taux_tva')
                ->comment('Montant de TVA pour cet article');

            // Ajouter le taux de commission
            $table->decimal('taux_commission', 5, 2)->nullable()->after('montant_tva')
                ->comment('Taux de commission appliqué à cet article');

            // Ajouter le montant de commission
            $table->decimal('montant_commission', 10, 2)->nullable()->after('taux_commission')
                ->comment('Montant de commission pour cet article');

            // Ajouter le montant total pour cet article
            $table->decimal('montant_total_ht', 10, 2)->nullable()->after('montant_commission')
                ->comment('Montant total HT (prix_unitaire_ht * quantite)');

            $table->decimal('montant_total_ttc', 10, 2)->nullable()->after('montant_total_ht')
                ->comment('Montant total TTC (prix_unitaire_ttc * quantite)');

            // Ajouter des informations sur le produit au moment de la commande
            $table->string('nom_produit_commande', 255)->nullable()->after('montant_total_ttc')
                ->comment('Nom du produit au moment de la commande');

            $table->text('description_produit_commande')->nullable()->after('nom_produit_commande')
                ->comment('Description du produit au moment de la commande');

            // Ajouter des informations de livraison spécifiques à l'article
            $table->decimal('poids_unitaire', 8, 2)->nullable()->after('description_produit_commande')
                ->comment('Poids unitaire du produit en kg');

            $table->decimal('poids_total', 8, 2)->nullable()->after('poids_unitaire')
                ->comment('Poids total (poids_unitaire * quantite)');

            // Statut spécifique de l'article
            $table->enum('statut_article', [
                'en_attente',
                'confirme',
                'en_preparation',
                'expedie',
                'livre',
                'retourne',
                'rembourse',
                'annule'
            ])->default('en_attente')->after('poids_total');

            // Informations de remboursement
            $table->boolean('eligible_remboursement')->default(true)->after('statut_article');
            $table->decimal('montant_rembourse', 10, 2)->default(0)->after('eligible_remboursement');

            // Métadonnées pour les variantes de produit
            $table->json('variantes_produit')->nullable()->after('montant_rembourse')
                ->comment('Informations sur les variantes (taille, couleur, etc.)');

            // Informations de promotion/réduction
            $table->decimal('montant_reduction', 10, 2)->default(0)->after('variantes_produit')
                ->comment('Montant de réduction appliqué');

            $table->string('code_promotion', 50)->nullable()->after('montant_reduction')
                ->comment('Code de promotion utilisé');

            // Ajouter des index pour optimiser les performances
            $table->index(['sous_commande_id', 'statut_article']);
            $table->index(['produit_id', 'statut_article']);
            $table->index(['eligible_remboursement']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('article_commandes', function (Blueprint $table) {
            // Supprimer les index
            $table->dropIndex(['sous_commande_id', 'statut_article']);
            $table->dropIndex(['produit_id', 'statut_article']);
            $table->dropIndex(['eligible_remboursement']);

            // Supprimer la contrainte de clé étrangère
            $table->dropForeign(['sous_commande_id']);

            // Remettre commande_id comme NOT NULL
            $table->foreignId('commande_id')->nullable(false)->change();

            // Supprimer toutes les colonnes ajoutées
            $table->dropColumn([
                'sous_commande_id',
                'prix_unitaire_ht',
                'prix_unitaire_ttc',
                'taux_tva',
                'montant_tva',
                'taux_commission',
                'montant_commission',
                'montant_total_ht',
                'montant_total_ttc',
                'nom_produit_commande',
                'description_produit_commande',
                'poids_unitaire',
                'poids_total',
                'statut_article',
                'eligible_remboursement',
                'montant_rembourse',
                'variantes_produit',
                'montant_reduction',
                'code_promotion'
            ]);
        });
    }
};
