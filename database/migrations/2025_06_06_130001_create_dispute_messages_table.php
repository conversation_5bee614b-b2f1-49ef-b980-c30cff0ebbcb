<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dispute_messages', function (Blueprint $table) {
            $table->uuid('id')->primary();
            
            // Relation avec le litige
            $table->foreignUuid('dispute_id')
                ->constrained('disputes')
                ->onDelete('cascade')
                ->comment('Litige concerné');
            
            // Auteur du message
            $table->enum('auteur_type', ['client', 'admin', 'marchand', 'system'])
                ->comment('Type d\'auteur du message');
            
            $table->foreignId('auteur_id')->nullable()
                ->comment('ID de l\'auteur (client_id, user_id, marchand_id)');
            
            $table->string('auteur_nom', 255)
                ->comment('Nom de l\'auteur pour affichage');
            
            // Contenu du message
            $table->text('message')
                ->comment('Contenu du message');
            
            $table->enum('type_message', [
                'message',          // Message normal
                'changement_statut', // Notification de changement de statut
                'piece_jointe',     // Ajout de pièce jointe
                'solution_proposee', // Proposition de solution
                'escalade',         // Notification d\'escalade
                'resolution',       // Message de résolution
                'system'            // Message système automatique
            ])->default('message')->comment('Type de message');
            
            // Pièces jointes
            $table->json('pieces_jointes')->nullable()
                ->comment('Fichiers joints au message');
            
            // Flags
            $table->boolean('interne')->default(false)
                ->comment('Message interne (visible seulement par admin)');
            $table->boolean('lu_par_client')->default(false)
                ->comment('Lu par le client');
            $table->boolean('lu_par_admin')->default(false)
                ->comment('Lu par l\'admin');
            $table->boolean('lu_par_marchand')->default(false)
                ->comment('Lu par le marchand');
            
            // Métadonnées
            $table->json('metadata')->nullable()
                ->comment('Données supplémentaires du message');
            
            $table->timestamps();
            
            // Index pour optimiser les performances
            $table->index(['dispute_id', 'created_at']);
            $table->index(['auteur_type', 'auteur_id']);
            $table->index(['type_message', 'created_at']);
            $table->index(['interne', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dispute_messages');
    }
};
