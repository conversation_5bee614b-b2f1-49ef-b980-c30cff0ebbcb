<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Adress;
use App\Models\Marchand;
use App\Models\User;

class MarchandFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Marchand::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'nomEntreprise' => fake()->regexify('[A-Za-z0-9]{255}'),
            'adresse_id' => Adress::factory(),
            'idFiscal' => fake()->regexify('[A-Za-z0-9]{50}'),
            'banqueNom' => fake()->regexify('[A-Za-z0-9]{100}'),
            'banqueNumeroCompte' => fake()->regexify('[A-Za-z0-9]{50}'),
        ];
    }
}
