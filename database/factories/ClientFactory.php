<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Adress;
use App\Models\Client;
use App\Models\User;

class ClientFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Client::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'prenom' => fake()->regexify('[A-Za-z0-9]{100}'),
            'nom' => fake()->regexify('[A-Za-z0-9]{100}'),
            'adresse_id' => Adress::factory(),
            'telephone' => fake()->regexify('[A-Za-z0-9]{20}'),
            'dateDeNaissance' => fake()->date(),
        ];
    }
}
