<?php

namespace App\Http\Controllers;

use App\Services\CheckoutService;
use App\Services\StockManagementService;
use App\Services\NotificationService;
use App\Models\Client;
use App\Models\Produit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DebugController extends Controller
{
    protected CheckoutService $checkoutService;
    protected StockManagementService $stockService;
    protected NotificationService $notificationService;

    public function __construct(
        CheckoutService $checkoutService,
        StockManagementService $stockService,
        NotificationService $notificationService
    ) {
        $this->checkoutService = $checkoutService;
        $this->stockService = $stockService;
        $this->notificationService = $notificationService;
    }

    /**
     * Test des services business
     */
    public function testServices()
    {
        try {
            $results = [];

            // Test 1: Services
            $results['services'] = [
                'CheckoutService' => class_exists(CheckoutService::class) ? 'OK' : 'ERREUR',
                'StockManagementService' => class_exists(StockManagementService::class) ? 'OK' : 'ERREUR',
                'NotificationService' => class_exists(NotificationService::class) ? 'OK' : 'ERREUR',
            ];

            // Test 2: Modèles
            $results['models'] = [
                'clients_count' => Client::count(),
                'produits_actifs' => Produit::where('statut', 'actif')->count(),
                'produits_en_stock' => Produit::where('statut', 'actif')->where('stock', '>', 0)->count(),
            ];

            // Test 3: Utilisateur connecté
            $user = Auth::user();
            $results['user'] = [
                'connected' => $user ? true : false,
                'user_id' => $user ? $user->id : null,
                'user_name' => $user ? $user->name : null,
                'has_client' => $user && $user->client ? true : false,
                'client_id' => $user && $user->client ? $user->client->id : null,
            ];

            // Test 4: Adresses
            if ($user) {
                $results['addresses'] = [
                    'count' => $user->adresses()->count(),
                    'addresses' => $user->adresses()->get()->map(function ($adresse) {
                        return [
                            'id' => $adresse->id,
                            'rue' => $adresse->rue,
                            'ville' => $adresse->ville,
                        ];
                    })
                ];
            }

            // Test 5: Test de stock simple
            $produits = Produit::where('statut', 'actif')
                ->where('stock', '>', 0)
                ->take(2)
                ->get();

            if ($produits->count() > 0) {
                $cartData = $produits->map(function ($produit) {
                    return [
                        'id' => $produit->id,
                        'quantity' => 1
                    ];
                })->toArray();

                $stockCheck = $this->stockService->checkStockAvailability($cartData);
                $results['stock_test'] = [
                    'cart_data' => $cartData,
                    'stock_check' => $stockCheck
                ];
            } else {
                $results['stock_test'] = [
                    'error' => 'Aucun produit disponible'
                ];
            }

            return response()->json([
                'success' => true,
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test services', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Test de création de commande simple
     */
    public function testOrderCreation(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user || !$user->client) {
                return response()->json([
                    'success' => false,
                    'error' => 'Utilisateur non connecté ou pas de client associé'
                ]);
            }

            // Récupérer des produits de test
            $produits = Produit::where('statut', 'actif')
                ->where('stock', '>', 0)
                ->take(2)
                ->get();

            if ($produits->count() === 0) {
                return response()->json([
                    'success' => false,
                    'error' => 'Aucun produit disponible'
                ]);
            }

            // Récupérer une adresse
            $adresse = $user->adresses()->first();
            if (!$adresse) {
                return response()->json([
                    'success' => false,
                    'error' => 'Aucune adresse trouvée'
                ]);
            }

            // Créer un panier de test
            $cartData = $produits->map(function ($produit) {
                return [
                    'id' => $produit->id,
                    'quantity' => 1,
                    'price' => $produit->prix
                ];
            })->toArray();

            Log::info('Test création commande', [
                'user_id' => $user->id,
                'client_id' => $user->client->id,
                'cart_data' => $cartData,
                'adresse_id' => $adresse->id
            ]);

            // Tester la création de commande
            $result = $this->checkoutService->createOrderFromCart(
                $cartData,
                $user->client->id,
                $adresse->id,
                $adresse->id,
                ['test' => true]
            );

            return response()->json([
                'success' => true,
                'message' => 'Commande créée avec succès',
                'data' => [
                    'commande_principale_id' => $result['commande_principale']->id,
                    'numero_commande' => $result['commande_principale']->numero_commande,
                    'montant_total' => $result['totals']['montant_total_ttc'],
                    'nombre_sous_commandes' => count($result['sous_commandes']),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur test création commande', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
