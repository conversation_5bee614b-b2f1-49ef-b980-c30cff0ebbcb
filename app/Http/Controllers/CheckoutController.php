<?php

namespace App\Http\Controllers;

use App\Services\CheckoutService;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Exception;

/**
 * Contrôleur pour gérer le processus de checkout
 */
class CheckoutController extends Controller
{
    protected CheckoutService $checkoutService;

    public function __construct(CheckoutService $checkoutService)
    {
        $this->checkoutService = $checkoutService;
    }

    /**
     * Crée une commande à partir du panier avant le paiement
     *
     * @param Request $request
     * @return \Inertia\Response|\Illuminate\Http\RedirectResponse
     */
    public function createOrder(Request $request)
    {
        try {
            // Validation des données
            $validated = $request->validate([
                'cart_items' => 'required|array|min:1',
                'cart_items.*.product_id' => 'required|integer|exists:produits,id',
                'cart_items.*.quantity' => 'required|integer|min:1',
                'cart_items.*.price' => 'required|numeric|min:0',
                'cart_items.*.delivery_fees' => 'nullable|numeric|min:0',
                'cart_items.*.zone_livraison_id' => 'nullable|integer|exists:zones_livraison,id',
                'cart_items.*.variants' => 'nullable|array',
                'cart_items.*.delivery_info' => 'nullable|array',
                'adresse_livraison_id' => 'required|integer|exists:adresses,id',
                'adresse_facturation_id' => 'nullable|integer|exists:adresses,id',
                'notes_client' => 'nullable|string|max:1000',
                'date_livraison_souhaitee' => 'nullable|date|after:today'
            ]);

            // Récupérer le client connecté
            $user = Auth::user();
            $client = Client::where('user_id', $user->id)->first();

            if (!$client) {
                return response()->json([
                    'success' => false,
                    'message' => 'Client non trouvé'
                ], 404);
            }

            // Préparer les données du panier
            $cartData = [
                'items' => $validated['cart_items']
            ];

            // Métadonnées supplémentaires
            $metadata = [
                'notes_client' => $validated['notes_client'] ?? null,
                'date_livraison_souhaitee' => $validated['date_livraison_souhaitee'] ?? null,
                'source' => 'web_checkout',
                'user_agent' => $request->userAgent(),
                'ip_address' => $request->ip()
            ];

            // Créer la commande
            Log::info('Création de commande - Début', [
                'client_id' => $client->id,
                'cart_data' => $cartData,
                'adresse_livraison_id' => $validated['adresse_livraison_id']
            ]);

            $result = $this->checkoutService->createOrderFromCart(
                $cartData,
                $client->id,
                $validated['adresse_livraison_id'],
                $validated['adresse_facturation_id'],
                $metadata
            );

            Log::info('Création de commande - Succès', [
                'commande_principale_id' => $result['commande_principale']->id,
                'numero_commande' => $result['commande_principale']->numero_commande
            ]);

            // Récupérer les adresses de l'utilisateur
            $user = $request->user();
            $adresses = $user->adresses()->get();
            // Retourner une réponse Inertia avec les données de la commande
            return Inertia::render('ecommerce/checkout', [
                'adresses' => $adresses,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email
                ],
                'createdOrder' => [
                    'commande_principale_id' => $result['commande_principale']->id,
                    'numero_commande' => $result['commande_principale']->numero_commande,
                    'montant_total' => $result['totals']['montant_total_ttc'],
                    'nombre_sous_commandes' => count($result['sous_commandes']),
                    'statut' => $result['commande_principale']->statut_global
                ],
                'flash' => [
                    'success' => true,
                    'message' => 'Commande créée avec succès'
                ]
            ]);

        } catch (ValidationException $e) {
            return back()->withErrors($e->errors())->withInput();

        } catch (Exception $e) {
            Log::error('Erreur lors de la création de commande', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Récupérer les adresses de l'utilisateur pour la réponse d'erreur
            $user = $request->user();
            $adresses = $user->adresses()->get();

            // Retourner une réponse Inertia avec l'erreur
            return Inertia::render('ecommerce/checkout', [
                'adresses' => $adresses,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email
                ],
                'flash' => [
                    'error' => true,
                    'message' => 'Erreur lors de la création de la commande: ' . $e->getMessage()
                ]
            ]);
        }
    }

    /**
     * Confirme le paiement d'une commande
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function confirmPayment(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'commande_principale_id' => 'required|integer|exists:commandes_principales,id',
                'payment_method' => 'required|string|in:paypal,card,orange,mtn',
                'transaction_id' => 'required|string',
                'payment_status' => 'required|string|in:completed,pending,failed',
                'payment_data' => 'nullable|array'
            ]);

            // Vérifier que la commande appartient au client connecté
            $user = Auth::user();
            $client = Client::where('user_id', $user->id)->first();

            $commandePrincipale = \App\Models\CommandePrincipale::where('id', $validated['commande_principale_id'])
                ->where('client_id', $client->id)
                ->first();

            if (!$commandePrincipale) {
                return response()->json([
                    'success' => false,
                    'message' => 'Commande non trouvée'
                ], 404);
            }

            // Confirmer le paiement seulement si le statut est "completed"
            if ($validated['payment_status'] === 'completed') {
                $paymentData = [
                    'method' => $validated['payment_method'],
                    'transaction_id' => $validated['transaction_id'],
                    'status' => $validated['payment_status'],
                    'data' => $validated['payment_data'] ?? []
                ];

                $this->checkoutService->confirmPayment(
                    $validated['commande_principale_id'],
                    $paymentData
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Paiement confirmé avec succès',
                    'data' => [
                        'commande_id' => $commandePrincipale->id,
                        'numero_commande' => $commandePrincipale->numero_commande,
                        'statut' => 'PayementConfirme'
                    ]
                ]);

            } elseif ($validated['payment_status'] === 'failed') {
                // Annuler la commande en cas d'échec
                $this->checkoutService->cancelOrder(
                    $validated['commande_principale_id'],
                    'Échec du paiement - Transaction ID: ' . $validated['transaction_id']
                );

                return response()->json([
                    'success' => false,
                    'message' => 'Paiement échoué, commande annulée',
                    'data' => [
                        'commande_id' => $commandePrincipale->id,
                        'statut' => 'Annulé'
                    ]
                ]);

            } else {
                // Paiement en attente
                return response()->json([
                    'success' => true,
                    'message' => 'Paiement en cours de traitement',
                    'data' => [
                        'commande_id' => $commandePrincipale->id,
                        'statut' => 'EnAttente'
                    ]
                ]);
            }

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Données invalides',
                'errors' => $e->errors()
            ], 422);

        } catch (Exception $e) {
            Log::error('Erreur lors de la confirmation de paiement', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la confirmation du paiement : ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Annule une commande
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function cancelOrder(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'commande_principale_id' => 'required|integer|exists:commandes_principales,id',
                'reason' => 'nullable|string|max:500'
            ]);

            // Vérifier que la commande appartient au client connecté
            $user = Auth::user();
            $client = Client::where('user_id', $user->id)->first();

            $commandePrincipale = \App\Models\CommandePrincipale::where('id', $validated['commande_principale_id'])
                ->where('client_id', $client->id)
                ->first();

            if (!$commandePrincipale) {
                return response()->json([
                    'success' => false,
                    'message' => 'Commande non trouvée'
                ], 404);
            }

            // Vérifier que la commande peut être annulée
            if (!$commandePrincipale->peutÊtreAnnulée()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cette commande ne peut plus être annulée'
                ], 400);
            }

            $reason = $validated['reason'] ?? 'Annulation par le client';

            $this->checkoutService->cancelOrder(
                $validated['commande_principale_id'],
                $reason
            );

            return response()->json([
                'success' => true,
                'message' => 'Commande annulée avec succès',
                'data' => [
                    'commande_id' => $commandePrincipale->id,
                    'statut' => 'Annulé'
                ]
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Données invalides',
                'errors' => $e->errors()
            ], 422);

        } catch (Exception $e) {
            Log::error('Erreur lors de l\'annulation de commande', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'annulation de la commande : ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupère les détails d'une commande
     *
     * @param int $commandeId
     * @return JsonResponse
     */
    public function getOrderDetails(int $commandeId): JsonResponse
    {
        try {
            $user = Auth::user();
            $client = Client::where('user_id', $user->id)->first();

            $commandePrincipale = \App\Models\CommandePrincipale::with([
                'sousCommandes.marchand',
                'sousCommandes.articles.produit',
                'adresseLivraison',
                'adresseFacturation'
            ])
                ->where('id', $commandeId)
                ->where('client_id', $client->id)
                ->first();

            if (!$commandePrincipale) {
                return response()->json([
                    'success' => false,
                    'message' => 'Commande non trouvée'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'commande' => $commandePrincipale,
                    'progression' => $commandePrincipale->getPourcentageProgression()
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Erreur lors de la récupération des détails de commande', [
                'user_id' => Auth::id(),
                'commande_id' => $commandeId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des détails de la commande'
            ], 500);
        }
    }
}
