<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BannerController extends Controller
{
    /**
     * Récupère toutes les bannières actives
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $banners = Banner::where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            })
            ->orderBy('priorite', 'desc')
            ->get();
        
        return response()->json($banners);
    }

    /**
     * Récupère les bannières actives pour une position spécifique
     *
     * @param string $position
     * @return JsonResponse
     */
    public function getByPosition(string $position): JsonResponse
    {
        $banners = Banner::where('is_active', true)
            ->where('position', $position)
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            })
            ->orderBy('priorite', 'desc')
            ->get();
        
        return response()->json($banners);
    }
}
