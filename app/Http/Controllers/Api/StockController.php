<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\StockManagementService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

/**
 * Contrôleur API pour la gestion des stocks
 */
class StockController extends Controller
{
    /**
     * Service de gestion des stocks
     */
    protected StockManagementService $stockService;

    /**
     * Constructeur
     */
    public function __construct(StockManagementService $stockService)
    {
        $this->stockService = $stockService;
    }

    /**
     * Vérifie la disponibilité des stocks pour un panier
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkAvailability(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'cart' => 'required|array',
            'cart.*.id' => 'required|integer|exists:produits,id',
            'cart.*.quantity' => 'required|integer|min:1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $cartData = $request->input('cart');
            $stockCheck = $this->stockService->checkStockAvailability($cartData);

            return response()->json([
                'success' => true,
                'data' => $stockCheck
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la vérification des stocks',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtient les informations de stock pour un produit spécifique
     *
     * @param int $productId
     * @return JsonResponse
     */
    public function getProductStock(int $productId): JsonResponse
    {
        try {
            $produit = \App\Models\Produit::find($productId);

            if (!$produit) {
                return response()->json([
                    'success' => false,
                    'message' => 'Produit non trouvé'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'product_id' => $produit->id,
                    'product_name' => $produit->nom,
                    'stock' => $produit->stock,
                    'available' => $produit->stock > 0,
                    'status' => $produit->statut
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération du stock',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtient les informations de stock pour plusieurs produits
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getMultipleProductsStock(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_ids' => 'required|array',
            'product_ids.*' => 'integer|exists:produits,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $productIds = $request->input('product_ids');
            $produits = \App\Models\Produit::whereIn('id', $productIds)->get();

            $stockData = $produits->map(function ($produit) {
                return [
                    'product_id' => $produit->id,
                    'product_name' => $produit->nom,
                    'stock' => $produit->stock,
                    'available' => $produit->stock > 0,
                    'status' => $produit->statut
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $stockData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des stocks',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
