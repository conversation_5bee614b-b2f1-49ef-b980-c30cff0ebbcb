<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BoutiqueReview;
use App\Models\BoutiqueReviewVote;
use App\Models\Marchand;
use App\Services\BoutiqueReviewImageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BoutiqueReviewController extends Controller
{
    /**
     * Récupère les avis d'une boutique
     *
     * @param string $marchandId
     * @return JsonResponse
     */
    public function getBoutiqueReviews(Request $request, string $marchandId): JsonResponse
    {
        $perPage = $request->input('per_page', 5); // Par défaut 5 avis par page
        $perPage = max(2, min(10, (int)$perPage)); // Limiter entre 2 et 10

        $sortBy = $request->input('sort_by', 'created_at'); // Par défaut tri par date de création
        $sortOrder = $request->input('sort_order', 'desc'); // Par défaut ordre décroissant
        $filterRating = $request->input('rating'); // Filtrer par note (optionnel)
        $withImages = $request->input('with_images'); // Filtrer les avis avec images (optionnel)
        $verified = $request->input('verified'); // Filtrer les avis vérifiés (optionnel)

        // Valider les paramètres de tri
        $validSortFields = ['created_at', 'rating', 'likes', 'dislikes'];
        if (!in_array($sortBy, $validSortFields)) {
            $sortBy = 'created_at';
        }

        $validSortOrders = ['asc', 'desc'];
        if (!in_array($sortOrder, $validSortOrders)) {
            $sortOrder = 'desc';
        }

        $query = BoutiqueReview::where('marchand_id', $marchandId)
            ->approved()
            ->with('user:id,name');

        // Appliquer le filtre par note si spécifié
        if ($filterRating !== null && is_numeric($filterRating)) {
            $query->withRating((int)$filterRating);
        }

        // Appliquer le filtre pour les avis avec images
        if ($withImages === 'true' || $withImages === '1') {
            $query->withImages();
        }

        // Appliquer le filtre pour les avis vérifiés
        if ($verified === 'true' || $verified === '1') {
            $query->verified();
        }

        // Appliquer le tri
        $query->orderBy($sortBy, $sortOrder);

        // Paginer les résultats
        $reviews = $query->paginate($perPage);

        return response()->json($reviews);
    }

    /**
     * Créer un nouvel avis pour une boutique
     *
     * @param Request $request
     * @param string $marchandId
     * @return JsonResponse
     */
    public function createBoutiqueReview(Request $request, string $marchandId): JsonResponse
    {
        // Valider les données
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'email' => 'nullable|email|max:255',
            'rating' => 'required|integer|min:1|max:5',
            'title' => 'nullable|string|max:255',
            'comment' => 'required|string|max:1000',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Vérifier que le marchand existe
        $marchand = Marchand::findOrFail($marchandId);

        // Vérifier si l'utilisateur a déjà laissé un avis (si connecté)
        if ($request->user()) {
            $existingReview = BoutiqueReview::where('marchand_id', $marchandId)
                ->where('user_id', $request->user()->id)
                ->first();

            if ($existingReview) {
                return response()->json([
                    'error' => 'Vous avez déjà laissé un avis pour cette boutique'
                ], 422);
            }
        }

        // Traiter les images via le service BoutiqueReviewImageService
        $imageNames = [];
        if ($request->hasFile('images')) {
            try {
                $boutiqueReviewImageService = new BoutiqueReviewImageService();
                $imageNames = $boutiqueReviewImageService->uploadBoutiqueReviewImages($request->file('images'), $marchandId);
            } catch (\Exception $e) {
                return response()->json([
                    'error' => 'Erreur lors de l\'upload des images: ' . $e->getMessage()
                ], 500);
            }
        }

        // Créer l'avis
        $review = new BoutiqueReview([
            'marchand_id' => $marchandId,
            'user_id' => $request->user()?->id,
            'name' => $request->name,
            'email' => $request->email,
            'rating' => $request->rating,
            'title' => $request->title,
            'comment' => $request->comment,
            'images' => $imageNames,
            'ip_address' => $request->ip(),
            'is_approved' => true, // Auto-approuvé pour l'instant
            'is_verified' => false, // À vérifier plus tard avec les commandes
        ]);

        $review->save();

        return response()->json($review, 201);
    }

    /**
     * Vote pour un avis boutique (like/dislike)
     *
     * @param Request $request
     * @param string $reviewId
     * @return JsonResponse
     */
    public function vote(Request $request, string $reviewId): JsonResponse
    {
        // Valider les données
        $validator = Validator::make($request->all(), [
            'vote_type' => 'required|in:like,dislike',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Vérifier si l'avis existe
        $review = BoutiqueReview::findOrFail($reviewId);

        // Vérifier si l'utilisateur a déjà voté
        $existingVote = BoutiqueReviewVote::where('boutique_review_id', $reviewId)
            ->where('ip_address', $request->ip())
            ->first();

        if ($existingVote) {
            // Si le vote est identique, ne rien faire
            if ($existingVote->vote_type === $request->vote_type) {
                return response()->json([
                    'message' => 'Vote déjà enregistré',
                    'likes' => $review->likes,
                    'dislikes' => $review->dislikes,
                ]);
            }

            // Mettre à jour les compteurs
            if ($existingVote->vote_type === 'like') {
                $review->likes--;
            } else {
                $review->dislikes--;
            }

            if ($request->vote_type === 'like') {
                $review->likes++;
            } else {
                $review->dislikes++;
            }

            // Mettre à jour le vote
            $existingVote->vote_type = $request->vote_type;
            $existingVote->save();
            $review->save();

            return response()->json([
                'message' => 'Vote mis à jour',
                'likes' => $review->likes,
                'dislikes' => $review->dislikes,
            ]);
        }

        // Créer un nouveau vote
        $vote = new BoutiqueReviewVote([
            'boutique_review_id' => $reviewId,
            'user_id' => $request->user()?->id,
            'ip_address' => $request->ip(),
            'vote_type' => $request->vote_type,
        ]);

        $vote->save();

        // Mettre à jour le compteur
        if ($request->vote_type === 'like') {
            $review->likes++;
        } else {
            $review->dislikes++;
        }
        $review->save();

        return response()->json([
            'message' => 'Vote enregistré',
            'likes' => $review->likes,
            'dislikes' => $review->dislikes,
        ]);
    }

    /**
     * Signaler un avis boutique
     *
     * @param Request $request
     * @param string $reviewId
     * @return JsonResponse
     */
    public function reportReview(Request $request, string $reviewId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $review = BoutiqueReview::findOrFail($reviewId);

        if (!$review->canBeReported()) {
            return response()->json([
                'error' => 'Cet avis ne peut pas être signalé'
            ], 422);
        }

        $review->markAsReported($request->reason);

        return response()->json([
            'message' => 'Avis signalé avec succès'
        ]);
    }

    /**
     * Obtenir les statistiques d'une boutique
     *
     * @param string $marchandId
     * @return JsonResponse
     */
    public function getBoutiqueStats(string $marchandId): JsonResponse
    {
        $marchand = Marchand::findOrFail($marchandId);

        return response()->json([
            'average_rating' => $marchand->boutique_average_rating,
            'total_reviews' => $marchand->boutique_reviews_count,
            'rating_stats' => $marchand->boutique_rating_stats,
        ]);
    }
}
