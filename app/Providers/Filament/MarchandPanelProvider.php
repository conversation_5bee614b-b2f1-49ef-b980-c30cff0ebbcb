<?php

namespace App\Providers\Filament;

use App\Http\Middleware\FilamentMarchandMiddleware;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class MarchandPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('marchand')
            ->path('marchand')
            ->colors([
                'primary' => Color::Emerald,
                'secondary' => Color::Indigo,
                'danger' => Color::Rose,
                'gray' => Color::Slate,
                'info' => Color::Sky,
                'success' => Color::Emerald,
                'warning' => Color::Orange,
                'active' => Color::hex("#28a745"),
                'premium' => Color::hex("#FFD700"),
                'elite' => Color::hex("#8A2BE2"),
                'gratuit' => Color::hex("#808080"),
                'basic' => Color::hex("#3498DB")
            ])
            ->brandName('Lorelei Marchand')
            ->favicon(asset('favicon.ico'))
            ->login()
            ->authGuard('web')
            ->discoverResources(in: app_path('Filament/Marchand/Resources'), for: 'App\\Filament\\Marchand\\Resources')
            ->discoverPages(in: app_path('Filament/Marchand/Pages'), for: 'App\\Filament\\Marchand\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->navigationGroups([
                'Dashboard',
                'Boutique & Produits',
                'Commandes & Expéditions',
                'Finances & Abonnements',
                'Marketing & Promotions',
                'Équipe & Collaboration',
                'Support & Communication',
                'Paramètres & Configuration',
            ])
            ->resources([
                \App\Filament\Marchand\Resources\PaiementResource::class,
                \App\Filament\Marchand\Resources\AbonnementResource::class,
                \App\Filament\Marchand\Resources\MarchandTeamResource::class,
                \App\Filament\Marchand\Resources\MarchandRoleResource::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Marchand/Widgets'), for: 'App\\Filament\\Marchand\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
                FilamentMarchandMiddleware::class,
            ]);
    }
}
