<?php

namespace App\Services;

use App\Models\ClientMarchandConversation;
use App\Models\ConversationMessage;
use App\Models\Client;
use App\Models\Marchand;
use App\Models\CommandePrincipale;
use App\Models\Produit;
use App\Services\NotificationService;
use App\Services\AdminNotificationService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Service de messagerie entre clients et marchands
 * Gère les conversations directes avant escalade vers litiges
 */
class ClientMarchandMessagingService
{
    protected NotificationService $notificationService;
    protected AdminNotificationService $adminNotificationService;

    public function __construct(NotificationService $notificationService, AdminNotificationService $adminNotificationService)
    {
        $this->notificationService = $notificationService;
        $this->adminNotificationService = $adminNotificationService;
    }

    /**
     * Crée une nouvelle conversation entre client et marchand
     *
     * @param array $conversationData
     * @return array
     */
    public function creerConversation(array $conversationData): array
    {
        return DB::transaction(function () use ($conversationData) {
            try {
                // Valider les données
                $this->validerDonneesConversation($conversationData);

                // Vérifier s'il existe déjà une conversation similaire
                $conversationExistante = $this->rechercherConversationExistante($conversationData);

                if ($conversationExistante && $conversationExistante->statut === 'active') {
                    return [
                        'success' => true,
                        'conversation' => $conversationExistante,
                        'nouvelle' => false,
                        'message' => 'Conversation existante trouvée'
                    ];
                }

                // Créer la nouvelle conversation
                $conversation = ClientMarchandConversation::creerConversation([
                    'client_id' => $conversationData['client_id'],
                    'marchand_id' => $conversationData['marchand_id'],
                    'commande_principale_id' => $conversationData['commande_principale_id'] ?? null,
                    'sous_commande_id' => $conversationData['sous_commande_id'] ?? null,
                    'produit_id' => $conversationData['produit_id'] ?? null,
                    'sujet' => $conversationData['sujet'],
                    'type_conversation' => $conversationData['type_conversation'],
                    'priorite' => $this->determinerPriorite($conversationData),
                    'cree_par_client_id' => $conversationData['client_id'],
                    'metadata' => [
                        'source' => $conversationData['source'] ?? 'web',
                        'user_agent' => request()->userAgent(),
                        'ip_address' => request()->ip(),
                    ]
                ]);

                // Ajouter le message initial
                $this->ajouterMessage($conversation->id, [
                    'auteur_type' => 'client',
                    'auteur_client_id' => $conversationData['client_id'],
                    'auteur_nom' => $this->obtenirNomClient($conversationData['client_id']),
                    'message' => $conversationData['message_initial'],
                    'type_message' => 'message',
                    'pieces_jointes' => $conversationData['pieces_jointes'] ?? [],
                ]);

                // Envoyer notification au marchand
                $this->envoyerNotificationNouvelleConversation($conversation);

                Log::info('Nouvelle conversation créée', [
                    'conversation_id' => $conversation->id,
                    'client_id' => $conversationData['client_id'],
                    'marchand_id' => $conversationData['marchand_id'],
                    'type' => $conversationData['type_conversation']
                ]);

                return [
                    'success' => true,
                    'conversation' => $conversation,
                    'nouvelle' => true,
                    'message' => 'Conversation créée avec succès'
                ];

            } catch (Exception $e) {
                Log::error('Erreur lors de la création de conversation', [
                    'conversation_data' => $conversationData,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                throw $e;
            }
        });
    }

    /**
     * Ajoute un message à une conversation
     *
     * @param string $conversationId
     * @param array $messageData
     * @return array
     */
    public function ajouterMessage(string $conversationId, array $messageData): array
    {
        try {
            $conversation = ClientMarchandConversation::findOrFail($conversationId);

            // Vérifier que la conversation est active
            if ($conversation->statut !== 'active') {
                throw new Exception('La conversation n\'est plus active');
            }

            // Créer le message
            $message = $conversation->ajouterMessage($messageData);

            // Marquer comme lu par l'auteur
            if ($messageData['auteur_type'] === 'client') {
                $message->marquerCommeLu('client');
            } elseif ($messageData['auteur_type'] === 'marchand') {
                $message->marquerCommeLu('marchand');
            }

            // Envoyer notification au destinataire
            $this->envoyerNotificationNouveauMessage($conversation, $message);

            // Envoyer webhook vers admin_marchand_lorrelei si c'est un message client
            if ($messageData['auteur_type'] === 'client') {
                try {
                    $this->adminNotificationService->notifyNewMessage($message);
                } catch (\Exception $e) {
                    Log::warning('Erreur envoi webhook admin pour nouveau message', [
                        'message_id' => $message->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info('Message ajouté à la conversation', [
                'conversation_id' => $conversationId,
                'message_id' => $message->id,
                'auteur_type' => $messageData['auteur_type']
            ]);

            return [
                'success' => true,
                'message' => $message,
                'conversation' => $conversation->fresh()
            ];

        } catch (Exception $e) {
            Log::error('Erreur lors de l\'ajout du message', [
                'conversation_id' => $conversationId,
                'message_data' => $messageData,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Marque les messages d'une conversation comme lus
     *
     * @param string $conversationId
     * @param string $participant
     * @return array
     */
    public function marquerCommeLu(string $conversationId, string $participant): array
    {
        try {
            $conversation = ClientMarchandConversation::findOrFail($conversationId);
            $conversation->marquerCommeLu($participant);

            return [
                'success' => true,
                'conversation' => $conversation->fresh()
            ];

        } catch (Exception $e) {
            Log::error('Erreur lors du marquage comme lu', [
                'conversation_id' => $conversationId,
                'participant' => $participant,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Ferme une conversation
     *
     * @param string $conversationId
     * @param string $fermePar
     * @param int $participantId
     * @return array
     */
    public function fermerConversation(string $conversationId, string $fermePar, int $participantId): array
    {
        try {
            $conversation = ClientMarchandConversation::findOrFail($conversationId);
            $conversation->fermer($fermePar, $participantId);

            // Envoyer notification à l'autre participant
            $this->envoyerNotificationConversationFermee($conversation, $fermePar);

            Log::info('Conversation fermée', [
                'conversation_id' => $conversationId,
                'ferme_par' => $fermePar,
                'participant_id' => $participantId
            ]);

            return [
                'success' => true,
                'conversation' => $conversation->fresh()
            ];

        } catch (Exception $e) {
            Log::error('Erreur lors de la fermeture de conversation', [
                'conversation_id' => $conversationId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Obtient les conversations d'un client
     *
     * @param int $clientId
     * @param array $filtres
     * @return array
     */
    public function obtenirConversationsClient(int $clientId, array $filtres = []): array
    {
        try {
            $query = ClientMarchandConversation::where('client_id', $clientId)
                ->with(['marchand', 'commandePrincipale', 'produit', 'messages' => function($q) {
                    $q->latest()->limit(1);
                }]);

            // Appliquer les filtres
            if (!empty($filtres['statut'])) {
                $query->where('statut', $filtres['statut']);
            }

            if (!empty($filtres['type'])) {
                $query->where('type_conversation', $filtres['type']);
            }

            if (!empty($filtres['marchand_id'])) {
                $query->where('marchand_id', $filtres['marchand_id']);
            }

            $conversations = $query->orderBy('date_dernier_message', 'desc')
                                 ->paginate($filtres['per_page'] ?? 10);

            return [
                'success' => true,
                'conversations' => $conversations
            ];

        } catch (Exception $e) {
            Log::error('Erreur lors de la récupération des conversations client', [
                'client_id' => $clientId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Obtient les statistiques de messagerie d'un client
     *
     * @param int $clientId
     * @return array
     */
    public function obtenirStatistiquesClient(int $clientId): array
    {
        try {
            $stats = [
                'total_conversations' => ClientMarchandConversation::where('client_id', $clientId)->count(),
                'conversations_actives' => ClientMarchandConversation::where('client_id', $clientId)->where('statut', 'active')->count(),
                'messages_non_lus' => ClientMarchandConversation::where('client_id', $clientId)
                    ->sum('messages_non_lus_client'),
                'conversations_recentes' => ClientMarchandConversation::where('client_id', $clientId)
                    ->where('date_dernier_message', '>=', now()->subDays(7))->count(),
            ];

            return [
                'success' => true,
                'stats' => $stats
            ];

        } catch (Exception $e) {
            Log::error('Erreur lors de la récupération des statistiques', [
                'client_id' => $clientId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Valide les données de création d'une conversation
     */
    private function validerDonneesConversation(array $data): void
    {
        $required = ['client_id', 'marchand_id', 'sujet', 'type_conversation', 'message_initial'];

        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new Exception("Le champ {$field} est requis");
            }
        }

        $typesValides = [
            'question_produit', 'probleme_commande', 'demande_info', 'reclamation', 'autre'
        ];

        if (!in_array($data['type_conversation'], $typesValides)) {
            throw new Exception("Type de conversation invalide");
        }
    }

    /**
     * Recherche une conversation existante similaire
     */
    private function rechercherConversationExistante(array $data): ?ClientMarchandConversation
    {
        $query = ClientMarchandConversation::where('client_id', $data['client_id'])
            ->where('marchand_id', $data['marchand_id']);

        // Si une commande est spécifiée, chercher par commande
        if (!empty($data['commande_principale_id'])) {
            $query->where('commande_principale_id', $data['commande_principale_id']);
        }
        // Sinon si un produit est spécifié, chercher par produit
        elseif (!empty($data['produit_id'])) {
            $query->where('produit_id', $data['produit_id']);
        }

        return $query->whereIn('statut', ['active', 'fermee'])
                    ->orderBy('date_creation', 'desc')
                    ->first();
    }

    /**
     * Détermine la priorité automatiquement
     */
    private function determinerPriorite(array $data): string
    {
        if ($data['type_conversation'] === 'reclamation') {
            return 'haute';
        }

        if ($data['type_conversation'] === 'probleme_commande') {
            return 'normale';
        }

        return 'basse';
    }

    /**
     * Obtient le nom d'un client
     */
    private function obtenirNomClient(int $clientId): string
    {
        $client = Client::find($clientId);
        return $client ? ($client->nom . ' ' . $client->prenom) : 'Client';
    }

    /**
     * Envoie une notification pour une nouvelle conversation
     */
    private function envoyerNotificationNouvelleConversation(ClientMarchandConversation $conversation): void
    {
        // TODO: Implémenter notification au marchand
        Log::info('Notification nouvelle conversation à implémenter', [
            'conversation_id' => $conversation->id
        ]);
    }

    /**
     * Envoie une notification pour un nouveau message
     */
    private function envoyerNotificationNouveauMessage(ClientMarchandConversation $conversation, ConversationMessage $message): void
    {
        // TODO: Implémenter notification nouveau message
        Log::info('Notification nouveau message à implémenter', [
            'conversation_id' => $conversation->id,
            'message_id' => $message->id
        ]);
    }

    /**
     * Envoie une notification pour une conversation fermée
     */
    private function envoyerNotificationConversationFermee(ClientMarchandConversation $conversation, string $fermePar): void
    {
        // TODO: Implémenter notification conversation fermée
        Log::info('Notification conversation fermée à implémenter', [
            'conversation_id' => $conversation->id,
            'ferme_par' => $fermePar
        ]);
    }
}
