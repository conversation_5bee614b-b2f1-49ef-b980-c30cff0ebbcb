<?php

namespace App\Services;

use App\Models\Produit;
use App\Models\ArticleCommande;
use App\Models\SousCommandeVendeur;
use App\Models\CommandePrincipale;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Service de gestion des stocks
 * Gère la réservation, libération et mise à jour des stocks lors des commandes
 */
class StockManagementService
{
    /**
     * Vérifie la disponibilité des stocks pour un panier
     *
     * @param array $cartData Données du panier
     * @return array Résultat de la vérification
     */
    public function checkStockAvailability(array $cartData): array
    {
        $unavailableItems = [];
        $warnings = [];

        foreach ($cartData as $item) {
            $produit = Produit::find($item['id']);

            if (!$produit) {
                $unavailableItems[] = [
                    'product_id' => $item['id'],
                    'reason' => 'Produit non trouvé',
                    'requested_quantity' => $item['quantity'],
                    'available_quantity' => 0
                ];
                continue;
            }

            // Vérifier si le produit est actif
            // if ($produit->statut !== 'actif') {
            //     $unavailableItems[] = [
            //         'product_id' => $item['id'],
            //         'product_name' => $produit->nom,
            //         'reason' => 'Produit non disponible',
            //         'requested_quantity' => $item['quantity'],
            //         'available_quantity' => 0
            //     ];
            //     continue;
            // }

            // Vérifier le stock disponible
            if ($produit->stock < $item['quantity']) {
                if ($produit->stock > 0) {
                    $warnings[] = [
                        'product_id' => $item['id'],
                        'product_name' => $produit->nom,
                        'reason' => 'Stock insuffisant',
                        'requested_quantity' => $item['quantity'],
                        'available_quantity' => $produit->stock
                    ];
                } else {
                    $unavailableItems[] = [
                        'product_id' => $item['id'],
                        'product_name' => $produit->nom,
                        'reason' => 'Rupture de stock',
                        'requested_quantity' => $item['quantity'],
                        'available_quantity' => 0
                    ];
                }
            }
        }

        return [
            'available' => empty($unavailableItems),
            'unavailable_items' => $unavailableItems,
            'warnings' => $warnings,
            'total_items_checked' => count($cartData)
        ];
    }

    /**
     * Réserve le stock pour une commande
     *
     * @param array $sousCommandes Liste des sous-commandes
     * @return array Résultat de la réservation
     * @throws Exception
     */
    public function reserveStock(array $sousCommandes): array
    {
        return DB::transaction(function () use ($sousCommandes) {
            $reservedItems = [];
            $errors = [];

            foreach ($sousCommandes as $sousCommande) {
                foreach ($sousCommande->articles as $article) {
                    try {
                        $result = $this->reserveStockForArticle($article);
                        if ($result['success']) {
                            $reservedItems[] = $result['data'];
                        } else {
                            $errors[] = $result['error'];
                        }
                    } catch (Exception $e) {
                        $errors[] = [
                            'article_id' => $article->id,
                            'product_id' => $article->produit_id,
                            'error' => $e->getMessage()
                        ];
                    }
                }
            }

            if (!empty($errors)) {
                // Rollback des réservations en cas d'erreur
                $this->rollbackReservations($reservedItems);
                throw new Exception('Erreur lors de la réservation des stocks: ' . json_encode($errors));
            }

            Log::info('Stocks réservés avec succès', [
                'reserved_items_count' => count($reservedItems),
                'sous_commandes_count' => count($sousCommandes)
            ]);

            return [
                'success' => true,
                'reserved_items' => $reservedItems,
                'total_reserved' => count($reservedItems)
            ];
        });
    }

    /**
     * Réserve le stock pour un article spécifique
     *
     * @param ArticleCommande $article
     * @return array
     */
    private function reserveStockForArticle(ArticleCommande $article): array
    {
        $produit = Produit::lockForUpdate()->find($article->produit_id);

        if (!$produit) {
            return [
                'success' => false,
                'error' => [
                    'article_id' => $article->id,
                    'product_id' => $article->produit_id,
                    'message' => 'Produit non trouvé'
                ]
            ];
        }

        if ($produit->stock < $article->quantite) {
            return [
                'success' => false,
                'error' => [
                    'article_id' => $article->id,
                    'product_id' => $article->produit_id,
                    'product_name' => $produit->nom,
                    'requested' => $article->quantite,
                    'available' => $produit->stock,
                    'message' => 'Stock insuffisant'
                ]
            ];
        }

        // Décrémenter le stock
        $ancienStock = $produit->stock;
        $produit->stock -= $article->quantite;
        $produit->save();

        Log::info('Stock réservé pour article', [
            'article_id' => $article->id,
            'product_id' => $produit->id,
            'product_name' => $produit->nom,
            'quantity_reserved' => $article->quantite,
            'stock_before' => $ancienStock,
            'stock_after' => $produit->stock
        ]);

        return [
            'success' => true,
            'data' => [
                'article_id' => $article->id,
                'product_id' => $produit->id,
                'product_name' => $produit->nom,
                'quantity_reserved' => $article->quantite,
                'stock_before' => $ancienStock,
                'stock_after' => $produit->stock
            ]
        ];
    }

    /**
     * Libère le stock en cas d'annulation de commande
     *
     * @param CommandePrincipale|SousCommandeVendeur $commande
     * @return array
     */
    public function releaseStock($commande): array
    {
        return DB::transaction(function () use ($commande) {
            $releasedItems = [];
            $errors = [];

            // Déterminer les articles selon le type de commande
            if ($commande instanceof CommandePrincipale) {
                $articles = $commande->articles;
            } elseif ($commande instanceof SousCommandeVendeur) {
                $articles = $commande->articles;
            } else {
                throw new Exception('Type de commande non supporté');
            }

            foreach ($articles as $article) {
                try {
                    $result = $this->releaseStockForArticle($article);
                    if ($result['success']) {
                        $releasedItems[] = $result['data'];
                    } else {
                        $errors[] = $result['error'];
                    }
                } catch (Exception $e) {
                    $errors[] = [
                        'article_id' => $article->id,
                        'product_id' => $article->produit_id,
                        'error' => $e->getMessage()
                    ];
                }
            }

            Log::info('Stocks libérés', [
                'commande_type' => get_class($commande),
                'commande_id' => $commande->id,
                'released_items_count' => count($releasedItems),
                'errors_count' => count($errors)
            ]);

            return [
                'success' => empty($errors),
                'released_items' => $releasedItems,
                'errors' => $errors,
                'total_released' => count($releasedItems)
            ];
        });
    }

    /**
     * Libère le stock pour un article spécifique
     *
     * @param ArticleCommande $article
     * @return array
     */
    private function releaseStockForArticle(ArticleCommande $article): array
    {
        $produit = Produit::lockForUpdate()->find($article->produit_id);

        if (!$produit) {
            return [
                'success' => false,
                'error' => [
                    'article_id' => $article->id,
                    'product_id' => $article->produit_id,
                    'message' => 'Produit non trouvé'
                ]
            ];
        }

        // Remettre le stock
        $ancienStock = $produit->stock;
        $produit->stock += $article->quantite;
        $produit->save();

        Log::info('Stock libéré pour article', [
            'article_id' => $article->id,
            'product_id' => $produit->id,
            'product_name' => $produit->nom,
            'quantity_released' => $article->quantite,
            'stock_before' => $ancienStock,
            'stock_after' => $produit->stock
        ]);

        return [
            'success' => true,
            'data' => [
                'article_id' => $article->id,
                'product_id' => $produit->id,
                'product_name' => $produit->nom,
                'quantity_released' => $article->quantite,
                'stock_before' => $ancienStock,
                'stock_after' => $produit->stock
            ]
        ];
    }

    /**
     * Rollback des réservations en cas d'erreur
     *
     * @param array $reservedItems
     * @return void
     */
    private function rollbackReservations(array $reservedItems): void
    {
        foreach ($reservedItems as $item) {
            try {
                $produit = Produit::find($item['product_id']);
                if ($produit) {
                    $produit->stock += $item['quantity_reserved'];
                    $produit->save();

                    Log::info('Stock rollback effectué', [
                        'product_id' => $produit->id,
                        'quantity_restored' => $item['quantity_reserved']
                    ]);
                }
            } catch (Exception $e) {
                Log::error('Erreur lors du rollback de stock', [
                    'product_id' => $item['product_id'],
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}
