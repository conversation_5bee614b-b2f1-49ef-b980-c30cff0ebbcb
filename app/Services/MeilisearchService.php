<?php

namespace App\Services;

use MeiliSearch\Client;
use MeiliSearch\Exceptions\MeiliSearchException;
use App\Models\Produit;
use App\Models\Categorie;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class MeilisearchService
{
    protected Client $client;
    protected array $config;

    public function __construct()
    {
        $this->config = config('meilisearch');
        $this->client = new Client($this->config['host'], $this->config['key']);
    }

    /**
     * Initialise les index Meilisearch
     */
    public function initializeIndexes(): void
    {
        try {
            // Vérifier d'abord la connexion à Meilisearch
            $this->client->health();
            Log::info('Connexion à Meilisearch établie avec succès');

            foreach ($this->config['indexes'] as $indexKey => $indexConfig) {
                Log::info("Initialisation de l'index: {$indexConfig['name']}");

                // Créer l'index s'il n'existe pas
                try {
                    $index = $this->client->index($indexConfig['name']);
                    $index->fetchInfo();
                    Log::info("Index {$indexConfig['name']} existe déjà");
                } catch (MeiliSearchException $e) {
                    if ($e->getCode() === 404) {
                        Log::info("Création de l'index {$indexConfig['name']}...");
                        $task = $this->client->createIndex($indexConfig['name'], ['primaryKey' => $indexConfig['primary_key']]);

                        // Attendre que la tâche soit terminée
                        $this->client->waitForTask($task['taskUid']);

                        $index = $this->client->index($indexConfig['name']);
                        Log::info("Index {$indexConfig['name']} créé avec succès");
                    } else {
                        Log::error("Erreur lors de l'accès à l'index {$indexConfig['name']}: " . $e->getMessage());
                        throw $e;
                    }
                }

                // Configurer les attributs de recherche
                if (isset($indexConfig['searchable_attributes'])) {
                    $index->updateSearchableAttributes($indexConfig['searchable_attributes']);
                }

                // Configurer les attributs filtrables
                if (isset($indexConfig['filterable_attributes'])) {
                    $index->updateFilterableAttributes($indexConfig['filterable_attributes']);
                }

                // Configurer les attributs triables
                if (isset($indexConfig['sortable_attributes'])) {
                    $index->updateSortableAttributes($indexConfig['sortable_attributes']);
                }

                // Configurer les règles de classement
                if (isset($indexConfig['ranking_rules'])) {
                    $index->updateRankingRules($indexConfig['ranking_rules']);
                }

                // Configurer les mots vides
                if (isset($indexConfig['stop_words'])) {
                    $index->updateStopWords($indexConfig['stop_words']);
                }

                // Configurer les synonymes
                if (isset($indexConfig['synonyms'])) {
                    $index->updateSynonyms($indexConfig['synonyms']);
                }

                Log::info("Index {$indexConfig['name']} initialisé avec succès");
            }
        } catch (MeiliSearchException $e) {
            Log::error('Erreur lors de l\'initialisation des index Meilisearch: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Indexe un produit dans Meilisearch
     */
    public function indexProduit(Produit $produit): bool
    {
        try {
            $index = $this->client->index($this->config['indexes']['produits']['name']);

            $document = $this->transformProduitForIndex($produit);
            $index->addDocuments([$document]);

            Log::info("Produit {$produit->id} indexé dans Meilisearch");
            return true;
        } catch (MeiliSearchException $e) {
            Log::error("Erreur lors de l'indexation du produit {$produit->id}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Indexe une catégorie dans Meilisearch
     */
    public function indexCategorie(Categorie $categorie): bool
    {
        try {
            $index = $this->client->index($this->config['indexes']['categories']['name']);

            $document = $this->transformCategorieForIndex($categorie);
            $index->addDocuments([$document]);

            Log::info("Catégorie {$categorie->id} indexée dans Meilisearch");
            return true;
        } catch (MeiliSearchException $e) {
            Log::error("Erreur lors de l'indexation de la catégorie {$categorie->id}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Indexe tous les produits
     */
    public function indexAllProduits(): void
    {
        try {
            $index = $this->client->index($this->config['indexes']['produits']['name']);

            Produit::with(['categorie', 'marchand', 'reviews'])
                ->chunk(100, function ($produits) use ($index) {
                    $documents = [];
                    foreach ($produits as $produit) {
                        $documents[] = $this->transformProduitForIndex($produit);
                    }
                    $index->addDocuments($documents);
                    Log::info('Indexé ' . count($documents) . ' produits');
                });

            Log::info('Indexation de tous les produits terminée');
        } catch (MeiliSearchException $e) {
            Log::error('Erreur lors de l\'indexation de tous les produits: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Indexe toutes les catégories
     */
    public function indexAllCategories(): void
    {
        try {
            $index = $this->client->index($this->config['indexes']['categories']['name']);

            Categorie::with(['categorieParent', 'produits'])
                ->chunk(100, function ($categories) use ($index) {
                    $documents = [];
                    foreach ($categories as $categorie) {
                        $documents[] = $this->transformCategorieForIndex($categorie);
                    }
                    $index->addDocuments($documents);
                    Log::info('Indexé ' . count($documents) . ' catégories');
                });

            Log::info('Indexation de toutes les catégories terminée');
        } catch (MeiliSearchException $e) {
            Log::error('Erreur lors de l\'indexation de toutes les catégories: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Supprime un produit de l'index
     */
    public function deleteProduit(int $produitId): bool
    {
        try {
            $index = $this->client->index($this->config['indexes']['produits']['name']);
            $index->deleteDocument($produitId);

            Log::info("Produit {$produitId} supprimé de l'index Meilisearch");
            return true;
        } catch (MeiliSearchException $e) {
            Log::error("Erreur lors de la suppression du produit {$produitId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Supprime une catégorie de l'index
     */
    public function deleteCategorie(int $categorieId): bool
    {
        try {
            $index = $this->client->index($this->config['indexes']['categories']['name']);
            $index->deleteDocument($categorieId);

            Log::info("Catégorie {$categorieId} supprimée de l'index Meilisearch");
            return true;
        } catch (MeiliSearchException $e) {
            Log::error("Erreur lors de la suppression de la catégorie {$categorieId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Transforme un produit pour l'indexation
     */
    private function transformProduitForIndex(Produit $produit): array
    {
        // Charger les relations si elles ne sont pas déjà chargées
        if (!$produit->relationLoaded('categorie')) {
            $produit->load('categorie');
        }
        if (!$produit->relationLoaded('marchand')) {
            $produit->load('marchand');
        }
        if (!$produit->relationLoaded('reviews')) {
            $produit->load('reviews');
        }

        return [
            'id' => $produit->id,
            'nom' => $produit->nom,
            'slug' => $produit->slug,
            'description' => $produit->description,
            'prix' => (float) $produit->prix,
            'stock' => $produit->stock,
            'images' => $produit->images,
            'product_code' => $produit->product_code,
            'marque' => $produit->marque,
            'categorie_id' => $produit->categorie_id,
            'categorie_nom' => $produit->categorie?->nom,
            'categorie_slug' => $produit->categorie?->slug,
            'marchand_id' => $produit->marchand_id,
            'marchand_nom' => $produit->marchand?->nomEntreprise,
            'discount_price' => $produit->discount_price ? (float) $produit->discount_price : null,
            'discount_start_date' => $produit->discount_start_date?->toISOString(),
            'discount_end_date' => $produit->discount_end_date?->toISOString(),
            'is_on_discount' => $this->isProductOnDiscount($produit),
            'average_rating' => $produit->reviews->avg('note') ?? 0,
            'reviews_count' => $produit->reviews->count(),
            'created_at' => $produit->created_at?->toISOString(),
            'updated_at' => $produit->updated_at?->toISOString(),
        ];
    }

    /**
     * Transforme une catégorie pour l'indexation
     */
    private function transformCategorieForIndex(Categorie $categorie): array
    {
        // Charger les relations si elles ne sont pas déjà chargées
        if (!$categorie->relationLoaded('categorieParent')) {
            $categorie->load('categorieParent');
        }
        if (!$categorie->relationLoaded('produits')) {
            $categorie->load('produits');
        }

        return [
            'id' => $categorie->id,
            'nom' => $categorie->nom,
            'slug' => $categorie->slug,
            'description' => $categorie->description,
            'categorie_parent_id' => $categorie->categorie_parent_id,
            'niveau' => $categorie->niveau,
            'category_path' => $categorie->category_path,
            'image_url' => $categorie->image_url,
            'has_products' => $categorie->produits->count() > 0,
            'products_count' => $categorie->produits->count(),
            'created_at' => $categorie->created_at?->toISOString(),
            'updated_at' => $categorie->updated_at?->toISOString(),
        ];
    }

    /**
     * Recherche des produits dans Meilisearch
     */
    public function searchProduits(string $query = '', array $filters = [], array $options = []): array
    {
        try {
            $index = $this->client->index($this->config['indexes']['produits']['name']);

            $searchParams = array_merge([
                'limit' => $this->config['search']['limit'],
                'offset' => $this->config['search']['offset'],
                'attributesToHighlight' => ['nom', 'description'],
                'highlightPreTag' => $this->config['search']['highlight_pre_tag'],
                'highlightPostTag' => $this->config['search']['highlight_post_tag'],
                'attributesToCrop' => ['description'],
                'cropLength' => $this->config['search']['crop_length'],
                'cropMarker' => $this->config['search']['crop_marker'],
                'matchesThreshold' => $this->config['search']['matches_threshold'],
                'facetsDistribution' => $this->config['search']['facets_distribution'] ? ['categorie_id', 'marchand_id'] : null,
            ], $options);

            if (!empty($filters)) {
                $searchParams['filter'] = $this->buildFilterString($filters);
            }

            $results = $index->search($query, $searchParams);

            return [
                'hits' => $results->getHits(),
                'total' => $results->getHitsCount(),
                'facets' => $results->getFacetsDistribution(),
                'query' => $query,
                'processing_time' => $results->getProcessingTimeMs(),
            ];
        } catch (MeiliSearchException $e) {
            Log::error('Erreur lors de la recherche de produits: ' . $e->getMessage());

            if ($this->config['fallback']['enabled']) {
                return $this->fallbackSearchProduits($query, $filters, $options);
            }

            throw $e;
        }
    }

    /**
     * Recherche des catégories dans Meilisearch
     */
    public function searchCategories(string $query = '', array $filters = [], array $options = []): array
    {
        try {
            $index = $this->client->index($this->config['indexes']['categories']['name']);

            $searchParams = array_merge([
                'limit' => $this->config['search']['limit'],
                'offset' => $this->config['search']['offset'],
                'attributesToHighlight' => ['nom', 'description'],
                'highlightPreTag' => $this->config['search']['highlight_pre_tag'],
                'highlightPostTag' => $this->config['search']['highlight_post_tag'],
            ], $options);

            if (!empty($filters)) {
                $searchParams['filter'] = $this->buildFilterString($filters);
            }

            $results = $index->search($query, $searchParams);

            return [
                'hits' => $results->getHits(),
                'total' => $results->getHitsCount(),
                'query' => $query,
                'processing_time' => $results->getProcessingTimeMs(),
            ];
        } catch (MeiliSearchException $e) {
            Log::error('Erreur lors de la recherche de catégories: ' . $e->getMessage());

            if ($this->config['fallback']['enabled']) {
                return $this->fallbackSearchCategories($query, $filters, $options);
            }

            throw $e;
        }
    }

    /**
     * Récupère des produits par catégorie depuis Meilisearch
     */
    public function getProduitsParCategorie(int $categorieId, array $options = []): array
    {
        $filters = ['categorie_id = ' . $categorieId];
        return $this->searchProduits('', $filters, $options);
    }

    /**
     * Récupère des produits en vedette depuis Meilisearch
     */
    public function getProduitsEnVedette(int $limit = 8): array
    {
        try {
            $index = $this->client->index($this->config['indexes']['produits']['name']);

            $results = $index->search('', [
                'limit' => $limit,
                'sort' => ['average_rating:desc', 'reviews_count:desc'],
            ]);

            return [
                'hits' => $results->getHits(),
                'total' => $results->getHitsCount(),
            ];
        } catch (MeiliSearchException $e) {
            Log::error('Erreur lors de la récupération des produits en vedette: ' . $e->getMessage());

            if ($this->config['fallback']['enabled']) {
                return $this->fallbackGetProduitsEnVedette($limit);
            }

            throw $e;
        }
    }

    /**
     * Récupère des produits en promotion depuis Meilisearch
     */
    public function getProduitsEnPromotion(array $options = []): array
    {
        $filters = ['is_on_discount = true'];
        return $this->searchProduits('', $filters, $options);
    }

    /**
     * Construit une chaîne de filtres pour Meilisearch
     */
    private function buildFilterString(array $filters): string
    {
        return implode(' AND ', $filters);
    }

    /**
     * Recherche de fallback pour les produits (base de données)
     */
    private function fallbackSearchProduits(string $query, array $filters, array $options): array
    {
        Log::info('Utilisation du fallback pour la recherche de produits');

        $queryBuilder = Produit::with(['categorie', 'marchand', 'reviews']);

        if (!empty($query)) {
            $queryBuilder->where(function($q) use ($query) {
                $q->where('nom', 'LIKE', "%{$query}%")
                  ->orWhere('description', 'LIKE', "%{$query}%")
                  ->orWhere('marque', 'LIKE', "%{$query}%");
            });
        }

        $limit = $options['limit'] ?? $this->config['search']['limit'];
        $offset = $options['offset'] ?? 0;

        $produits = $queryBuilder->skip($offset)->take($limit)->get();
        $total = $queryBuilder->count();

        return [
            'hits' => $produits->map(function($produit) {
                return $this->transformProduitForIndex($produit);
            })->toArray(),
            'total' => $total,
            'facets' => [],
            'query' => $query,
            'processing_time' => 0,
            'fallback' => true,
        ];
    }

    /**
     * Recherche de fallback pour les catégories (base de données)
     */
    private function fallbackSearchCategories(string $query, array $filters, array $options): array
    {
        Log::info('Utilisation du fallback pour la recherche de catégories');

        $queryBuilder = Categorie::with(['categorieParent', 'produits']);

        if (!empty($query)) {
            $queryBuilder->where(function($q) use ($query) {
                $q->where('nom', 'LIKE', "%{$query}%")
                  ->orWhere('description', 'LIKE', "%{$query}%");
            });
        }

        $limit = $options['limit'] ?? $this->config['search']['limit'];
        $offset = $options['offset'] ?? 0;

        $categories = $queryBuilder->skip($offset)->take($limit)->get();
        $total = $queryBuilder->count();

        return [
            'hits' => $categories->map(function($categorie) {
                return $this->transformCategorieForIndex($categorie);
            })->toArray(),
            'total' => $total,
            'query' => $query,
            'processing_time' => 0,
            'fallback' => true,
        ];
    }

    /**
     * Fallback pour les produits en vedette
     */
    private function fallbackGetProduitsEnVedette(int $limit): array
    {
        Log::info('Utilisation du fallback pour les produits en vedette');

        $produits = Produit::with(['categorie', 'marchand', 'reviews'])
            ->inRandomOrder()
            ->limit($limit)
            ->get();

        return [
            'hits' => $produits->map(function($produit) {
                return $this->transformProduitForIndex($produit);
            })->toArray(),
            'total' => $produits->count(),
            'fallback' => true,
        ];
    }

    /**
     * Vérifie si un produit est en promotion
     */
    private function isProductOnDiscount(Produit $produit): bool
    {
        if (!$produit->discount_price || !$produit->discount_start_date || !$produit->discount_end_date) {
            return false;
        }

        $now = Carbon::now();
        return $now->between($produit->discount_start_date, $produit->discount_end_date);
    }
}
