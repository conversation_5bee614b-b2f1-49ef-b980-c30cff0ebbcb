<?php

namespace App\Services;

use App\Models\Dispute;
use App\Models\DisputeMessage;
use App\Models\CommandePrincipale;
use App\Models\SousCommandeVendeur;
use App\Models\Client;
use App\Models\EscrowTransaction;
use App\Services\EscrowService;
use App\Services\NotificationService;
use App\Services\AdminNotificationService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Exception;

/**
 * Service de gestion des litiges
 * Gère la création, le traitement et la résolution des litiges clients
 */
class DisputeService
{
    protected EscrowService $escrowService;
    protected NotificationService $notificationService;
    protected AdminNotificationService $adminNotificationService;

    public function __construct(
        EscrowService $escrowService,
        NotificationService $notificationService,
        AdminNotificationService $adminNotificationService
    ) {
        $this->escrowService = $escrowService;
        $this->notificationService = $notificationService;
        $this->adminNotificationService = $adminNotificationService;
    }

    /**
     * Crée un nouveau litige
     *
     * @param array $disputeData
     * @return array
     */
    public function creerLitige(array $disputeData): array
    {
        return DB::transaction(function () use ($disputeData) {
            try {
                // Valider les données
                $this->validerDonneesLitige($disputeData);

                // Récupérer la commande principale
                $commande = CommandePrincipale::with(['sousCommandes', 'client'])
                    ->findOrFail($disputeData['commande_principale_id']);

                // Générer un numéro de litige unique
                $numeroLitige = Dispute::generateNumeroLitige();

                // Déterminer la priorité automatiquement
                $priorite = $this->determinerPriorite($disputeData['type_litige'], $disputeData);

                // Créer le litige
                $dispute = Dispute::create([
                    'commande_principale_id' => $commande->id,
                    'sous_commande_id' => $disputeData['sous_commande_id'] ?? null,
                    'client_id' => $commande->client_id,
                    'marchand_id' => $disputeData['marchand_id'] ?? null,
                    'numero_litige' => $numeroLitige,
                    'type_litige' => $disputeData['type_litige'],
                    'priorite' => $priorite,
                    'sujet' => $disputeData['sujet'],
                    'description' => $disputeData['description'],
                    'solution_souhaitee' => $disputeData['solution_souhaitee'] ?? null,
                    'montant_conteste' => $disputeData['montant_conteste'] ?? null,
                    'date_ouverture' => now(),
                    'pieces_jointes' => $disputeData['pieces_jointes'] ?? [],
                    'metadata' => [
                        'source' => $disputeData['source'] ?? 'web',
                        'user_agent' => request()->userAgent(),
                        'ip_address' => request()->ip(),
                        'commande_details' => [
                            'numero_commande' => $commande->numero_commande,
                            'montant_total' => $commande->montant_total_ttc,
                            'statut_global' => $commande->statut_global
                        ]
                    ]
                ]);

                // Calculer et mettre à jour la date limite de réponse
                $dispute->updateDateLimiteReponse();

                // Lier à la transaction escrow si elle existe
                $this->lierTransactionEscrow($dispute, $commande);

                // Créer le message initial
                $this->creerMessageInitial($dispute, $disputeData);

                // Assigner automatiquement si configuré
                $this->assignerAutomatiquement($dispute);

                // Envoyer les notifications
                $this->envoyerNotificationsOuverture($dispute);

                // Envoyer webhook vers admin_marchand_lorrelei pour nouveau litige
                try {
                    $this->adminNotificationService->notifyNewDispute($dispute);
                } catch (\Exception $e) {
                    Log::warning('Erreur envoi webhook admin pour nouveau litige', [
                        'dispute_id' => $dispute->id,
                        'error' => $e->getMessage()
                    ]);
                }

                Log::info('Litige créé avec succès', [
                    'dispute_id' => $dispute->id,
                    'numero_litige' => $numeroLitige,
                    'client_id' => $commande->client_id,
                    'type_litige' => $disputeData['type_litige']
                ]);

                return [
                    'success' => true,
                    'dispute' => $dispute,
                    'numero_litige' => $numeroLitige,
                    'message' => 'Litige créé avec succès'
                ];

            } catch (Exception $e) {
                Log::error('Erreur lors de la création du litige', [
                    'dispute_data' => $disputeData,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                throw $e;
            }
        });
    }

    /**
     * Ajoute un message à un litige
     *
     * @param string $disputeId
     * @param array $messageData
     * @return array
     */
    public function ajouterMessage(string $disputeId, array $messageData): array
    {
        try {
            $dispute = Dispute::findOrFail($disputeId);

            // Créer le message
            $message = DisputeMessage::create([
                'dispute_id' => $disputeId,
                'auteur_type' => $messageData['auteur_type'],
                'auteur_id' => $messageData['auteur_id'],
                'auteur_nom' => $messageData['auteur_nom'],
                'message' => $messageData['message'],
                'type_message' => $messageData['type_message'] ?? 'message',
                'pieces_jointes' => $messageData['pieces_jointes'] ?? [],
                'interne' => $messageData['interne'] ?? false,
                'metadata' => $messageData['metadata'] ?? []
            ]);

            // Marquer comme lu par l'auteur
            $message->marquerCommeLu($messageData['auteur_type']);

            // Mettre à jour le statut du litige si nécessaire
            $this->mettreAJourStatutApresMessage($dispute, $messageData['auteur_type']);

            // Envoyer les notifications
            $this->envoyerNotificationsNouveauMessage($dispute, $message);

            // Envoyer webhook vers admin_marchand_lorrelei si c'est un message client
            if ($messageData['auteur_type'] === 'client') {
                try {
                    $this->adminNotificationService->notifyNewDisputeMessage($message);
                } catch (\Exception $e) {
                    Log::warning('Erreur envoi webhook admin pour nouveau message de litige', [
                        'message_id' => $message->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info('Message ajouté au litige', [
                'dispute_id' => $disputeId,
                'message_id' => $message->id,
                'auteur_type' => $messageData['auteur_type']
            ]);

            return [
                'success' => true,
                'message' => $message,
                'dispute' => $dispute
            ];

        } catch (Exception $e) {
            Log::error('Erreur lors de l\'ajout du message', [
                'dispute_id' => $disputeId,
                'message_data' => $messageData,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Change le statut d'un litige
     *
     * @param string $disputeId
     * @param string $nouveauStatut
     * @param int|null $userId
     * @param string|null $commentaire
     * @return array
     */
    public function changerStatut(string $disputeId, string $nouveauStatut, ?int $userId = null, ?string $commentaire = null): array
    {
        return DB::transaction(function () use ($disputeId, $nouveauStatut, $userId, $commentaire) {
            try {
                $dispute = Dispute::findOrFail($disputeId);
                $ancienStatut = $dispute->statut;

                // Valider le changement de statut
                $this->validerChangementStatut($dispute, $nouveauStatut);

                // Changer le statut
                $dispute->changerStatut($nouveauStatut, $userId, $commentaire);

                // Créer un message de changement de statut
                DisputeMessage::creerMessageChangementStatut(
                    $disputeId,
                    $ancienStatut,
                    $nouveauStatut,
                    $userId,
                    $commentaire
                );

                // Actions spécifiques selon le nouveau statut
                $this->executerActionsStatut($dispute, $nouveauStatut, $userId);

                // Envoyer les notifications
                $this->envoyerNotificationsChangementStatut($dispute, $ancienStatut, $nouveauStatut);

                Log::info('Statut du litige changé', [
                    'dispute_id' => $disputeId,
                    'ancien_statut' => $ancienStatut,
                    'nouveau_statut' => $nouveauStatut,
                    'user_id' => $userId
                ]);

                return [
                    'success' => true,
                    'dispute' => $dispute->fresh(),
                    'ancien_statut' => $ancienStatut,
                    'nouveau_statut' => $nouveauStatut
                ];

            } catch (Exception $e) {
                Log::error('Erreur lors du changement de statut', [
                    'dispute_id' => $disputeId,
                    'nouveau_statut' => $nouveauStatut,
                    'error' => $e->getMessage()
                ]);

                throw $e;
            }
        });
    }

    /**
     * Résout un litige avec remboursement
     *
     * @param string $disputeId
     * @param array $resolutionData
     * @return array
     */
    public function resoudreAvecRemboursement(string $disputeId, array $resolutionData): array
    {
        return DB::transaction(function () use ($disputeId, $resolutionData) {
            try {
                $dispute = Dispute::with(['commandePrincipale', 'escrowTransaction'])->findOrFail($disputeId);

                // Valider les données de résolution
                $this->validerDonneesResolution($resolutionData);

                $montantRemboursement = $resolutionData['montant_remboursement'];
                $raisonRemboursement = $resolutionData['raison'] ?? 'Résolution de litige';

                // Effectuer le remboursement via EscrowService
                if ($dispute->escrowTransaction) {
                    $remboursementResult = $this->escrowService->refundFundsToClient(
                        $dispute->commandePrincipale,
                        $raisonRemboursement,
                        $montantRemboursement
                    );

                    if (!$remboursementResult['success']) {
                        throw new Exception('Échec du remboursement: ' . ($remboursementResult['error'] ?? 'Erreur inconnue'));
                    }
                } else {
                    // Remboursement direct si pas d'escrow
                    Log::warning('Remboursement direct sans escrow', [
                        'dispute_id' => $disputeId,
                        'montant' => $montantRemboursement
                    ]);
                }

                // Mettre à jour le litige
                $dispute->update([
                    'statut' => 'resolu',
                    'montant_rembourse' => $montantRemboursement,
                    'montant_compensation' => $resolutionData['montant_compensation'] ?? 0,
                    'resolution_details' => $resolutionData['details'],
                    'date_resolution' => now(),
                    'date_fermeture' => now(),
                    'resolu_par' => $resolutionData['resolu_par'] ?? null
                ]);

                // Créer un message de résolution
                DisputeMessage::create([
                    'dispute_id' => $disputeId,
                    'auteur_type' => 'admin',
                    'auteur_id' => $resolutionData['resolu_par'] ?? null,
                    'auteur_nom' => 'Administration',
                    'message' => "Litige résolu avec remboursement de {$montantRemboursement} FCFA.\n\nDétails: {$resolutionData['details']}",
                    'type_message' => 'resolution',
                    'metadata' => [
                        'montant_remboursement' => $montantRemboursement,
                        'montant_compensation' => $resolutionData['montant_compensation'] ?? 0,
                        'remboursement_result' => $remboursementResult ?? null
                    ]
                ]);

                // Envoyer les notifications
                $this->envoyerNotificationsResolution($dispute, $montantRemboursement);

                Log::info('Litige résolu avec remboursement', [
                    'dispute_id' => $disputeId,
                    'montant_remboursement' => $montantRemboursement,
                    'resolu_par' => $resolutionData['resolu_par'] ?? null
                ]);

                return [
                    'success' => true,
                    'dispute' => $dispute->fresh(),
                    'montant_rembourse' => $montantRemboursement,
                    'remboursement_result' => $remboursementResult ?? null
                ];

            } catch (Exception $e) {
                Log::error('Erreur lors de la résolution avec remboursement', [
                    'dispute_id' => $disputeId,
                    'resolution_data' => $resolutionData,
                    'error' => $e->getMessage()
                ]);

                throw $e;
            }
        });
    }

    /**
     * Traite les litiges en retard pour escalade automatique
     *
     * @return array
     */
    public function traiterEscaladeAutomatique(): array
    {
        try {
            $disputesEnRetard = Dispute::enRetard()
                ->where('escalade_automatique', true)
                ->where('statut', 'en_cours')
                ->with(['assigneA'])
                ->get();

            $escalades = [];
            $erreurs = [];

            foreach ($disputesEnRetard as $dispute) {
                try {
                    $this->escaladerLitige($dispute);
                    $escalades[] = [
                        'dispute_id' => $dispute->id,
                        'numero_litige' => $dispute->numero_litige,
                        'retard_heures' => now()->diffInHours($dispute->date_limite_reponse)
                    ];
                } catch (Exception $e) {
                    $erreurs[] = [
                        'dispute_id' => $dispute->id,
                        'error' => $e->getMessage()
                    ];
                }
            }

            Log::info('Traitement escalade automatique terminé', [
                'disputes_traites' => count($escalades),
                'erreurs' => count($erreurs)
            ]);

            return [
                'success' => true,
                'escalades' => $escalades,
                'erreurs' => $erreurs,
                'total_traites' => count($escalades)
            ];

        } catch (Exception $e) {
            Log::error('Erreur lors du traitement d\'escalade automatique', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Obtient les statistiques des litiges
     *
     * @return array
     */
    public function getStatistiques(): array
    {
        try {
            $stats = [
                'total_litiges' => Dispute::count(),
                'litiges_ouverts' => Dispute::ouverts()->count(),
                'litiges_en_retard' => Dispute::enRetard()->count(),
                'litiges_urgents' => Dispute::urgents()->count(),
                'litiges_resolus_aujourd_hui' => Dispute::where('statut', 'resolu')
                    ->whereDate('date_resolution', today())->count(),
                'temps_moyen_resolution' => $this->calculerTempsMoyenResolution(),
                'satisfaction_moyenne' => Dispute::whereNotNull('satisfaction_client')
                    ->avg('satisfaction_client'),
                'repartition_par_type' => $this->getRepartitionParType(),
                'repartition_par_statut' => $this->getRepartitionParStatut(),
                'montant_total_rembourse' => Dispute::sum('montant_rembourse')
            ];

            return [
                'success' => true,
                'stats' => $stats
            ];

        } catch (Exception $e) {
            Log::error('Erreur lors de la récupération des statistiques', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Valide les données de création d'un litige
     */
    private function validerDonneesLitige(array $data): void
    {
        $required = ['commande_principale_id', 'type_litige', 'sujet', 'description'];

        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new Exception("Le champ {$field} est requis");
            }
        }

        $typesValides = [
            'non_livraison', 'produit_defectueux', 'produit_different',
            'livraison_partielle', 'retard_livraison', 'frais_supplementaires',
            'service_client', 'autre'
        ];

        if (!in_array($data['type_litige'], $typesValides)) {
            throw new Exception("Type de litige invalide");
        }
    }

    /**
     * Détermine la priorité automatiquement selon le type de litige
     */
    private function determinerPriorite(string $typeLitige, array $data): string
    {
        // Priorité urgente pour certains types
        if (in_array($typeLitige, ['non_livraison', 'produit_defectueux'])) {
            return 'haute';
        }

        // Priorité selon le montant contesté
        if (isset($data['montant_conteste'])) {
            $montant = (float) $data['montant_conteste'];
            if ($montant > 100000) return 'haute';      // > 100k FCFA
            if ($montant > 50000) return 'normale';     // > 50k FCFA
            return 'basse';
        }

        return 'normale';
    }

    /**
     * Lie le litige à une transaction escrow si elle existe
     */
    private function lierTransactionEscrow(Dispute $dispute, CommandePrincipale $commande): void
    {
        $escrowTransaction = EscrowTransaction::where('commande_principale_id', $commande->id)
            ->whereIn('statut', ['held', 'disputed'])
            ->first();

        if ($escrowTransaction) {
            $dispute->update(['escrow_transaction_id' => $escrowTransaction->id]);

            // Marquer la transaction escrow comme en litige
            if (!$escrowTransaction->en_litige) {
                $escrowTransaction->markAsDisputed(
                    "Litige ouvert: {$dispute->numero_litige} - {$dispute->sujet}"
                );
            }
        }
    }

    /**
     * Crée le message initial du litige
     */
    private function creerMessageInitial(Dispute $dispute, array $disputeData): void
    {
        DisputeMessage::create([
            'dispute_id' => $dispute->id,
            'auteur_type' => 'client',
            'auteur_id' => $dispute->client_id,
            'auteur_nom' => $dispute->client->nom . ' ' . $dispute->client->prenom,
            'message' => $disputeData['description'],
            'type_message' => 'message',
            'pieces_jointes' => $disputeData['pieces_jointes'] ?? [],
            'lu_par_client' => true
        ]);
    }

    /**
     * Assigne automatiquement le litige selon les règles configurées
     */
    private function assignerAutomatiquement(Dispute $dispute): void
    {
        // TODO: Implémenter la logique d'assignation automatique
        // Peut être basée sur la charge de travail, l'expertise, etc.
        Log::info('Assignation automatique à implémenter', [
            'dispute_id' => $dispute->id
        ]);
    }

    /**
     * Envoie les notifications d'ouverture de litige
     */
    private function envoyerNotificationsOuverture(Dispute $dispute): void
    {
        try {
            // Notification au client via base de données
            $client = $dispute->client;
            if ($client && $client->user) {
                $this->notificationService->sendMultiChannelNotification(
                    $client->user,
                    'litige_ouvert',
                    [
                        'title' => 'Litige ouvert',
                        'message' => "Votre litige {$dispute->numero_litige} a été ouvert et sera traité dans les plus brefs délais.",
                        'dispute_id' => $dispute->id,
                        'numero_litige' => $dispute->numero_litige,
                        'action_url' => route('client.disputes.show', $dispute->id) ?? '#'
                    ],
                    ['database'] // Seulement notification en base pour l'instant
                );
            }

            // Notification aux admins
            // TODO: Implémenter notification aux admins

        } catch (Exception $e) {
            Log::error('Erreur envoi notifications ouverture litige', [
                'dispute_id' => $dispute->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Met à jour le statut du litige après un nouveau message
     */
    private function mettreAJourStatutApresMessage(Dispute $dispute, string $auteurType): void
    {
        if ($auteurType === 'client' && $dispute->statut === 'attente_client') {
            $dispute->changerStatut('en_cours');
        } elseif ($auteurType === 'marchand' && $dispute->statut === 'attente_marchand') {
            $dispute->changerStatut('en_cours');
        }
    }

    /**
     * Envoie les notifications pour un nouveau message
     */
    private function envoyerNotificationsNouveauMessage(Dispute $dispute, DisputeMessage $message): void
    {
        // TODO: Implémenter notifications nouveau message
        Log::info('Notifications nouveau message à implémenter', [
            'dispute_id' => $dispute->id,
            'message_id' => $message->id
        ]);
    }

    /**
     * Valide un changement de statut
     */
    private function validerChangementStatut(Dispute $dispute, string $nouveauStatut): void
    {
        $statutsValides = [
            'ouvert', 'en_cours', 'attente_client', 'attente_marchand',
            'resolu', 'ferme_admin', 'ferme_client', 'escalade', 'expire'
        ];

        if (!in_array($nouveauStatut, $statutsValides)) {
            throw new Exception("Statut invalide: {$nouveauStatut}");
        }

        // Règles de transition
        if ($dispute->statut === 'resolu' && $nouveauStatut !== 'ferme_admin') {
            throw new Exception("Un litige résolu ne peut être que fermé par admin");
        }
    }

    /**
     * Exécute les actions spécifiques selon le nouveau statut
     */
    private function executerActionsStatut(Dispute $dispute, string $nouveauStatut, ?int $userId = null): void
    {
        switch ($nouveauStatut) {
            case 'escalade':
                $this->escaladerLitige($dispute);
                break;
            case 'resolu':
            case 'ferme_admin':
            case 'ferme_client':
                $this->fermerLitige($dispute);
                break;
        }
    }

    /**
     * Escalade un litige
     */
    private function escaladerLitige(Dispute $dispute): void
    {
        $dispute->update([
            'statut' => 'escalade',
            'priorite' => 'urgente',
            'urgent' => true
        ]);

        DisputeMessage::creerMessageSysteme(
            $dispute->id,
            'Litige escaladé automatiquement en raison du dépassement du délai de traitement.',
            ['escalade_automatique' => true, 'ancien_priorite' => $dispute->priorite]
        );
    }

    /**
     * Ferme un litige
     */
    private function fermerLitige(Dispute $dispute): void
    {
        if (!$dispute->date_fermeture) {
            $dispute->update(['date_fermeture' => now()]);
        }
    }

    /**
     * Envoie les notifications de changement de statut
     */
    private function envoyerNotificationsChangementStatut(Dispute $dispute, string $ancienStatut, string $nouveauStatut): void
    {
        // TODO: Implémenter notifications changement statut
        Log::info('Notifications changement statut à implémenter', [
            'dispute_id' => $dispute->id,
            'ancien_statut' => $ancienStatut,
            'nouveau_statut' => $nouveauStatut
        ]);
    }

    /**
     * Valide les données de résolution
     */
    private function validerDonneesResolution(array $data): void
    {
        if (empty($data['montant_remboursement']) || $data['montant_remboursement'] <= 0) {
            throw new Exception("Montant de remboursement invalide");
        }

        if (empty($data['details'])) {
            throw new Exception("Les détails de résolution sont requis");
        }
    }

    /**
     * Envoie les notifications de résolution
     */
    private function envoyerNotificationsResolution(Dispute $dispute, float $montantRemboursement): void
    {
        // TODO: Implémenter notifications résolution
        Log::info('Notifications résolution à implémenter', [
            'dispute_id' => $dispute->id,
            'montant_remboursement' => $montantRemboursement
        ]);
    }

    /**
     * Calcule le temps moyen de résolution
     */
    private function calculerTempsMoyenResolution(): ?float
    {
        $resolus = Dispute::where('statut', 'resolu')
            ->whereNotNull('date_resolution')
            ->whereNotNull('date_ouverture')
            ->get();

        if ($resolus->isEmpty()) {
            return null;
        }

        $totalHeures = $resolus->sum(function ($dispute) {
            return $dispute->date_ouverture->diffInHours($dispute->date_resolution);
        });

        return round($totalHeures / $resolus->count(), 2);
    }

    /**
     * Obtient la répartition par type de litige
     */
    private function getRepartitionParType(): array
    {
        return Dispute::selectRaw('type_litige, COUNT(*) as count')
            ->groupBy('type_litige')
            ->pluck('count', 'type_litige')
            ->toArray();
    }

    /**
     * Obtient la répartition par statut
     */
    private function getRepartitionParStatut(): array
    {
        return Dispute::selectRaw('statut, COUNT(*) as count')
            ->groupBy('statut')
            ->pluck('count', 'statut')
            ->toArray();
    }
}
