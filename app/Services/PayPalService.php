<?php

namespace App\Services;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Mo<PERSON>y\Matcher\Any;
use Srmklive\PayPal\Services\PayPal as PayPalClient;



class PayPalService
{
    private $apiContext;
    private $clientId;
    private $clientSecret;
    private $mode;
    private $provider;

    public function __construct()
    {
        $this->clientId = config('paypal.sandbox.client_id');
        $this->clientSecret = config('paypal.sandbox.client_secret');
        $this->mode = config('paypal.sandbox.mode');
        $this->provider = new PayPalClient;
    }

    /**
     * Créer un paiement PayPal
     *
     * @param array $orderData
     * @return array
     */
    public function createPayment(array $orderData)
    {
        try {
            // Configuration du payeur
            $this->provider->setApiCredentials(config('paypal'));
            $paypalToken = $this->provider->getAccessToken();
            $this->provider->setAccessToken($paypalToken);

            $order = $this->provider->createOrder([
                'intent' => 'CAPTURE',
                'application_context' => [
                    'return_url' => $orderData['return_url'],
                    'cancel_url' => $orderData['cancel_url'],
                ],
                'purchase_units' => [
                    [
                        'amount' => [
                            'currency_code' => $orderData['currency'],
                            'value' => $orderData['montant']
                        ]
                    ]
                ]
            ]);
            if (isset($order['id']) && $order['status'] === 'CREATED') {
                $approvalUrl = $this->getApprovalUrl($order['links']);
                return [
                    'success' => true,
                    'order' => $order,
                    'approval_url' => $approvalUrl
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Order not created',
                    'order' => $order
                ];
            }
        } catch (\Exception $e) {
            Log::error('PayPal Payment Creation Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }


    public function capturePayment(Request $request)
    {
        try {
            $this->provider->setApiCredentials(config('paypal'));
            $paypalToken = $this->provider->getAccessToken();
            $this->provider->setAccessToken($paypalToken);

            $capture = $this->provider->capturePaymentOrder($request->token);
            return [
                'success' => true,
                'capture' => $capture
            ];
        } catch (\Exception $e) {
            Log::error('PayPal Capture Payment Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Exécuter un paiement PayPal
     *
     * @param string $paymentId
     * @param string $payerId
     * @return array
     */
    public function executePayment(string $paymentId, string $payerId): array
    {
        try {

            return [];
        } catch (\Exception $e) {
            Log::error('PayPal Payment Execution Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Obtenir les détails d'un paiement
     *
     * @param string $paymentId
     * @return array
     */
    public function capturePaymentDetails(string $captureId): array
    {
        try {
            $this->provider->setApiCredentials(config('paypal'));
            $paypalToken = $this->provider->getAccessToken();
            $this->provider->setAccessToken($paypalToken);
            $capture = $this->provider->showCapturedPaymentDetails($captureId);
            return [
                'success' => true,
                'capture' => $capture
            ];
        } catch (\Exception $e) {
            Log::error('PayPal Get Payment Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Rembourser un paiement
     *
     * @param string $saleId
     * @param float|null $amount
     * @return array
     */
    public function refundPayment(string $captureId, string $invoiceId, ?float $amount = null, string $note): array
    {
        try {
            $this->provider->setApiCredentials(config('paypal'));
            $paypalToken = $this->provider->getAccessToken();
            $this->provider->setAccessToken($paypalToken);
            if ($amount !== null) {
                $amount = $this->formatAmount($amount);
            }
            if (!empty($note)) {
                $note = $note;
            } else {
                $note = 'Refunded by ' . auth()->user()->name . ' on ' . now()->format('d/m/Y H:i:s');
            }
            $refund = $this->provider->refundCapturedPayment($captureId, $invoiceId, $amount, $note);

            Log::info('PayPal Refund Attempt: ' . ['captureId' => $captureId, 'invoiceId' => $invoiceId, 'amount' => $amount, 'note' => $note]);

            if (isset($refund['status']) && in_array($refund['status'], ['COMPLETED', 'PENDING'])) {
                return [
                    'success' => true,
                    'refund' => $refund
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $refund['details'][0]['description'] ?? 'Refund failed for unknown reason.',
                    'refund' => $refund
                ];
            }
        } catch (\Exception $e) {
            Log::error('PayPal Refund Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    public function getRefundDetails(string $refundId): array
    {
        try {
            $this->provider->setApiCredentials(config('paypal'));
            $paypalToken = $this->provider->getAccessToken();
            $this->provider->setAccessToken($paypalToken);
            $refund = $this->provider->showRefundDetails($refundId);
            return [
                'success' => true,
                'refund' => $refund
            ];
        } catch (\Exception $e) {
            Log::error('PayPal Get Refund Details Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    public function sendPayout(string $receiverEmail, float $amount, string $currency, string $note = '', string $senderBatchHeader, string $phoneNumber, string $countryCode): array
    {
        try {
            $this->provider->setApiCredentials(config('paypal'));
            $paypalToken = $this->provider->getAccessToken();
            $this->provider->setAccessToken($paypalToken);

            // Générer un sender_batch_header unique si non fourni
            if ($senderBatchHeader === null) {
                $senderBatchHeader = 'Payout_' . uniqid();
            }

            $payouts = [
                'sender_batch_header' => [
                    'sender_batch_id' => $senderBatchHeader,
                    'email_subject' => 'Votre paiement de ' . config('app.name'), // Sujet de l'email pour le bénéficiaire
                    'email_message' => $note ?: 'Vous avez reçu un paiement pour vos ventes sur ' . config('app.name') . '. Merci pour votre confiance.',
                ],
                'items' => [
                    [
                        'recipient_type' => 'EMAIL',
                        'receiver' => $receiverEmail,
                        'amount' => [
                            'value' => $this->formatAmount($amount),
                            'currency' => $currency,
                        ],
                        'note' => $note ?: 'Paiement pour vos ventes. ',
                        'sender_item_id' => 'Item_' . uniqid(), // ID unique pour cet élément du lot
                        "alternate_notification_method" => [
                            "phone" => [
                                "country_code" => $countryCode,
                                "phone_number" => $phoneNumber
                            ]
                        ],
                        "notification_language" => "fr_FR"
                    ],
                ],
            ];

            // Utilise la méthode createAndSendPayouts de la bibliothèque
            $response = $this->provider->createBatchPayout($payouts);

            Log::info('PayPal Payout Attempt:', ['receiver' => $receiverEmail, 'amount' => $amount, 'response' => $response]);

            // Vérifiez la réponse de PayPal
            if (isset($response['batch_header']['batch_status']) && in_array($response['batch_header']['batch_status'], ['PENDING', 'SUCCESS', 'PROCESSING'])) {
                return [
                    'success' => true,
                    'payout_batch_id' => $response['batch_header']['payout_batch_id'],
                    'status' => $response['batch_header']['batch_status'],
                    'response' => $response
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $response['name'] ?? 'Payout failed for unknown reason.',
                    'details' => $response
                ];
            }
        } catch (\Exception $e) {
            Log::error('PayPal Payout Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    public function getPayoutDetails(string $payoutId): array
    {
        try {
            $this->provider->setApiCredentials(config('paypal'));
            $paypalToken = $this->provider->getAccessToken();
            $this->provider->setAccessToken($paypalToken);
            $payout = $this->provider->showBatchPayoutDetails($payoutId);
            return [
                'success' => true,
                'payout' => $payout
            ];
        } catch (\Exception $e) {
            Log::error('PayPal Get Payout Details Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    public function getPayoutItemDetails(string $payout_item_id): array
    {
        try {
            $this->provider->setApiCredentials(config('paypal'));
            $paypalToken = $this->provider->getAccessToken();
            $this->provider->setAccessToken($paypalToken);
            $item = $this->provider->showPayoutItemDetails($payout_item_id);
            return [
                'success' => true,
                'item' => $item
            ];
        } catch (\Exception $e) {
            Log::error('PayPal Get Payout Item Details Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    public function cancelUnclaimedPayout(string $payout_item_id): array
    {
        try {
            $this->provider->setApiCredentials(config('paypal'));
            $paypalToken = $this->provider->getAccessToken();
            $this->provider->setAccessToken($paypalToken);
            $cancel = $this->provider->cancelUnclaimedPayoutItem($payout_item_id);
            return [
                'success' => true,
                'cancel' => $cancel
            ];
        } catch (\Exception $e) {
            Log::error('PayPal Cancel Unclaimed Payout Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Vérifier un webhook PayPal
     *
     * @param array $headers
     * @param string $body
     * @return bool
     */
    public function verifyWebhook(array $headers, string $body): bool
    {
        try {
            $webhookId = config('services.paypal.webhook_id');

            if (!$webhookId) {
                Log::warning('PayPal Webhook ID not configured');
                return false;
            }

            // Ici vous pouvez implémenter la vérification du webhook
            // selon la documentation PayPal

            return true;
        } catch (\Exception $e) {
            Log::error('PayPal Webhook Verification Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtenir l'URL d'approbation du paiement
     *
     * @param string $paymentId
     * @return string|null
     */
    private function getApprovalUrl(array $links)
    {

        $approvalUrl = null;
        foreach ($links as $link) {
            if ($link['rel'] === 'approve') {
                session()->put('paypal_approval_url', $link['href']);
                $approvalUrl = $link['href'];
                break;
            }
        }
        return $approvalUrl;
    }

    /**
     * Convertir un montant en centimes pour PayPal
     *
     * @param float $amount
     * @return string
     */
    public function formatAmount(float $amount): string
    {
        return number_format($amount, 2, '.', '');
    }

    /**
     * Vérifier si PayPal est configuré
     *
     * @return bool
     */
    public function isConfigured(): bool
    {
        return !empty($this->clientId) && !empty($this->clientSecret);
    }
}
