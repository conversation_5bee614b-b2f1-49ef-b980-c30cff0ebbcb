<?php

namespace App\Observers;

use App\Models\Produit;
use App\Services\MeilisearchService;
use Illuminate\Support\Facades\Log;

class ProduitObserver
{
    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Handle the Produit "created" event.
     */
    public function created(Produit $produit): void
    {
        try {
            // Recharger le produit avec ses relations pour s'assurer que les accessors fonctionnent
            $produitFrais = Produit::with(['categorie', 'marchand', 'reviews'])->find($produit->id);

            if ($produitFrais) {
                $this->meilisearchService->indexProduit($produitFrais);
                Log::info("Produit {$produit->id} ajouté à l'index Meilisearch après création");
            }
        } catch (\Exception $e) {
            Log::error("Erreur lors de l'indexation du produit {$produit->id} après création: " . $e->getMessage());
        }
    }

    /**
     * Handle the Produit "updated" event.
     */
    public function updated(Produit $produit): void
    {
        try {
            // Recharger le produit avec ses relations pour s'assurer que les accessors fonctionnent
            $produitFrais = Produit::with(['categorie', 'marchand', 'reviews'])->find($produit->id);

            if ($produitFrais) {
                $this->meilisearchService->indexProduit($produitFrais);
                Log::info("Produit {$produit->id} mis à jour dans l'index Meilisearch");
            }
        } catch (\Exception $e) {
            Log::error("Erreur lors de la mise à jour du produit {$produit->id} dans Meilisearch: " . $e->getMessage());
        }
    }

    /**
     * Handle the Produit "deleted" event.
     */
    public function deleted(Produit $produit): void
    {
        try {
            $this->meilisearchService->deleteProduit($produit->id);
            Log::info("Produit {$produit->id} supprimé de l'index Meilisearch");
        } catch (\Exception $e) {
            Log::error("Erreur lors de la suppression du produit {$produit->id} de Meilisearch: " . $e->getMessage());
        }
    }

    /**
     * Handle the Produit "restored" event.
     */
    public function restored(Produit $produit): void
    {
        try {
            $this->meilisearchService->indexProduit($produit);
            Log::info("Produit {$produit->id} restauré dans l'index Meilisearch");
        } catch (\Exception $e) {
            Log::error("Erreur lors de la restauration du produit {$produit->id} dans Meilisearch: " . $e->getMessage());
        }
    }

    /**
     * Handle the Produit "force deleted" event.
     */
    public function forceDeleted(Produit $produit): void
    {
        try {
            $this->meilisearchService->deleteProduit($produit->id);
            Log::info("Produit {$produit->id} définitivement supprimé de l'index Meilisearch");
        } catch (\Exception $e) {
            Log::error("Erreur lors de la suppression définitive du produit {$produit->id} de Meilisearch: " . $e->getMessage());
        }
    }
}
