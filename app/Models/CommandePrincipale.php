<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CommandePrincipale extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'commandes_principales';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'client_id',
        'numero_commande',
        'montant_total_ht',
        'montant_total_ttc',
        'montant_commission_plateforme',
        'montant_taxes',
        'statut_global',
        'adresse_livraison_id',
        'adresse_facturation_id',
        'methode_paiement',
        'reference_paiement_externe',
        'statut_paiement',
        'date_commande',
        'date_livraison_souhaitee',
        'date_paiement',
        'date_confirmation',
        'date_premiere_expedition',
        'date_derniere_expedition',
        'date_livraison_complete',
        'instructions_speciales',
        'notes_internes',
        'devise',
        'nombre_articles_total',
        'nombre_marchands',
        'nombre_sous_commandes',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'montant_total_ht' => 'decimal:2',
        'montant_total_ttc' => 'decimal:2',
        'montant_commission_plateforme' => 'decimal:2',
        'montant_taxes' => 'decimal:2',
        'date_commande' => 'datetime',
        'date_livraison_souhaitee' => 'date',
        'date_paiement' => 'datetime',
        'date_confirmation' => 'datetime',
        'date_premiere_expedition' => 'datetime',
        'date_derniere_expedition' => 'datetime',
        'date_livraison_complete' => 'datetime',
        'nombre_articles_total' => 'integer',
        'nombre_marchands' => 'integer',
        'nombre_sous_commandes' => 'integer',
        'metadata' => 'array',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'frais_livraison_total',
        'frais_livraison_total_formate',
    ];

    /**
     * Relation avec le client
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Relation avec l'adresse de livraison
     */
    public function adresseLivraison(): BelongsTo
    {
        return $this->belongsTo(Adresse::class, 'adresse_livraison_id');
    }

    /**
     * Relation avec l'adresse de facturation
     */
    public function adresseFacturation(): BelongsTo
    {
        return $this->belongsTo(Adresse::class, 'adresse_facturation_id');
    }

    /**
     * Relation avec les sous-commandes vendeur
     */
    public function sousCommandes(): HasMany
    {
        return $this->hasMany(SousCommandeVendeur::class);
    }

    /**
     * Relation avec les commandes legacy (pour la transition)
     */
    public function commandesLegacy(): HasMany
    {
        return $this->hasMany(Commande::class);
    }

    /**
     * Relation avec les remboursements
     */
    public function remboursements(): HasMany
    {
        return $this->hasMany(Remboursement::class);
    }

    /**
     * Scopes pour les requêtes courantes
     */
    public function scopeEnAttente($query)
    {
        return $query->where('statut_global', 'EnAttente');
    }

    public function scopeEnTraitement($query)
    {
        return $query->where('statut_global', 'EnTraitement');
    }

    public function scopeTerminee($query)
    {
        return $query->where('statut_global', 'Terminé');
    }

    public function scopePourClient($query, $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Vérifie si la commande est complètement expédiée
     */
    public function estComplètementExpédiée(): bool
    {
        return $this->statut_global === 'TotalementExpedié';
    }

    /**
     * Vérifie si la commande est complètement livrée
     */
    public function estComplètementLivrée(): bool
    {
        return $this->statut_global === 'TotalementLivré';
    }

    /**
     * Vérifie si la commande peut être annulée
     */
    public function peutÊtreAnnulée(): bool
    {
        return in_array($this->statut_global, ['EnAttente', 'PayementConfirme', 'EnTraitement']);
    }

    /**
     * Calcule le pourcentage de progression de la commande
     */
    public function getPourcentageProgression(): int
    {
        $statuts = [
            'EnAttente' => 0,
            'PayementConfirme' => 20,
            'EnTraitement' => 40,
            'PartielExpedié' => 60,
            'TotalementExpedié' => 80,
            'TotalementLivré' => 100,
            'Terminé' => 100,
        ];

        return $statuts[$this->statut_global] ?? 0;
    }

    /**
     * Calcule le total des frais de livraison de toutes les sous-commandes
     */
    public function getFraisLivraisonTotalAttribute(): float
    {
        return $this->sousCommandes->sum('frais_livraison');
    }

    /**
     * Obtient le total des frais de livraison formaté
     */
    public function getFraisLivraisonTotalFormateAttribute(): string
    {
        $total = $this->frais_livraison_total;
        return number_format($total, 0, ',', ' ') . ' FCFA';
    }

    /**
     * Génère un numéro de commande unique
     */
    public static function genererNumeroCommande(): string
    {
        $prefix = 'CMD';
        $date = now()->format('Ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

        $numero = $prefix . $date . $random;

        // Vérifier l'unicité
        while (self::where('numero_commande', $numero)->exists()) {
            $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $numero = $prefix . $date . $random;
        }

        return $numero;
    }

    /**
     * Met à jour le statut global basé sur les sous-commandes
     */
    public function mettreAJourStatutGlobal(): void
    {
        $sousCommandes = $this->sousCommandes;

        if ($sousCommandes->isEmpty()) {
            return;
        }

        $statutsUniques = $sousCommandes->pluck('statut')->unique();

        // Logique de détermination du statut global
        if ($statutsUniques->contains('Annulé') && $statutsUniques->count() === 1) {
            $this->statut_global = 'Annulé';
        } elseif ($statutsUniques->contains('Livré') && $statutsUniques->every(fn($statut) => in_array($statut, ['Livré', 'Annulé']))) {
            $this->statut_global = $statutsUniques->contains('Annulé') ? 'PartielLivré' : 'TotalementLivré';
        } elseif ($statutsUniques->contains('Expédié') && $statutsUniques->every(fn($statut) => in_array($statut, ['Expédié', 'Livré', 'Annulé']))) {
            $this->statut_global = $statutsUniques->contains('Annulé') ? 'PartielExpedié' : 'TotalementExpedié';
        } elseif ($statutsUniques->every(fn($statut) => in_array($statut, ['EnPreparation', 'PrêtExpédition', 'Confirmé']))) {
            $this->statut_global = 'EnTraitement';
        }

        $this->save();
    }
}
