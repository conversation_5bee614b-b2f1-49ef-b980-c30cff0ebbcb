<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class EscrowTransaction extends Model
{
    use HasFactory, HasUuids;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'escrow_transactions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'commande_principale_id',
        'transaction_id',
        'payment_method',
        'external_reference',
        'montant_total',
        'devise',
        'montant_original',
        'devise_originale',
        'taux_conversion',
        'statut',
        'date_hold',
        'date_release',
        'date_refund',
        'date_expiration',
        'delai_contestation_heures',
        'delai_securite_heures',
        'raison_refund',
        'montant_rembourse',
        'refund_transaction_id',
        'montant_libere',
        'nombre_versements_crees',
        'en_litige',
        'date_debut_litige',
        'details_litige',
        'cree_par_service',
        'traite_par',
        'metadata',
        'payment_data',
        'release_data',
        'refund_data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'montant_total' => 'decimal:2',
        'montant_original' => 'decimal:2',
        'taux_conversion' => 'decimal:6',
        'montant_rembourse' => 'decimal:2',
        'montant_libere' => 'decimal:2',
        'date_hold' => 'datetime',
        'date_release' => 'datetime',
        'date_refund' => 'datetime',
        'date_expiration' => 'datetime',
        'date_debut_litige' => 'datetime',
        'delai_contestation_heures' => 'integer',
        'delai_securite_heures' => 'integer',
        'nombre_versements_crees' => 'integer',
        'en_litige' => 'boolean',
        'metadata' => 'array',
        'payment_data' => 'array',
        'release_data' => 'array',
        'refund_data' => 'array',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'devise' => 'FCFA',
        'statut' => 'held',
        'delai_contestation_heures' => 168,
        'delai_securite_heures' => 48,
        'montant_rembourse' => 0,
        'montant_libere' => 0,
        'nombre_versements_crees' => 0,
        'en_litige' => false,
        'cree_par_service' => 'EscrowService',
    ];

    /**
     * Relation avec la commande principale
     */
    public function commandePrincipale(): BelongsTo
    {
        return $this->belongsTo(CommandePrincipale::class);
    }

    /**
     * Relation avec l'utilisateur qui a traité la transaction
     */
    public function traiteur(): BelongsTo
    {
        return $this->belongsTo(User::class, 'traite_par');
    }

    /**
     * Vérifie si la transaction est éligible pour libération
     */
    public function isEligibleForRelease(): bool
    {
        // Doit être en statut 'held'
        if ($this->statut !== 'held') {
            return false;
        }

        // Ne doit pas être en litige
        if ($this->en_litige) {
            return false;
        }

        // Vérifier que toutes les sous-commandes sont livrées
        $sousCommandes = $this->commandePrincipale->sousCommandes;
        foreach ($sousCommandes as $sousCommande) {
            if ($sousCommande->statut !== 'livre') {
                return false;
            }
        }

        // Vérifier le délai de sécurité (48h après livraison)
        $dateLivraisonComplete = $this->commandePrincipale->date_livraison_complete;
        if (!$dateLivraisonComplete) {
            // Si pas de date de livraison complète, utiliser la dernière livraison
            $derniereLivraison = $sousCommandes->max('date_livraison_reelle');
            if (!$derniereLivraison) {
                return false;
            }
            $dateLivraisonComplete = $derniereLivraison;
        }

        $delaiSecurite = now()->subHours($this->delai_securite_heures);
        return $dateLivraisonComplete <= $delaiSecurite;
    }

    /**
     * Vérifie si le délai de contestation est expiré
     */
    public function isContestationExpired(): bool
    {
        if (!$this->date_expiration) {
            return false;
        }

        return now() > $this->date_expiration;
    }

    /**
     * Calcule la date d'expiration du délai de contestation
     */
    public function calculateExpirationDate(): \DateTime
    {
        $dateLivraisonComplete = $this->commandePrincipale->date_livraison_complete;

        if (!$dateLivraisonComplete) {
            // Utiliser la dernière livraison des sous-commandes
            $derniereLivraison = $this->commandePrincipale->sousCommandes->max('date_livraison_reelle');
            $dateLivraisonComplete = $derniereLivraison ?: now();
        }

        return $dateLivraisonComplete->addHours($this->delai_contestation_heures);
    }

    /**
     * Met à jour la date d'expiration
     */
    public function updateExpirationDate(): void
    {
        $this->update([
            'date_expiration' => $this->calculateExpirationDate()
        ]);
    }

    /**
     * Marque la transaction comme en litige
     */
    public function markAsDisputed(string $details): void
    {
        $this->update([
            'statut' => 'disputed',
            'en_litige' => true,
            'date_debut_litige' => now(),
            'details_litige' => $details
        ]);
    }

    /**
     * Génère un ID de transaction unique
     */
    public static function generateTransactionId(string $paymentMethod, int $commandePrincipaleId): string
    {
        $prefix = match($paymentMethod) {
            'paypal' => 'ESC-PP',
            'orange' => 'ESC-OM',
            'mtn' => 'ESC-MTN',
            'card' => 'ESC-CARD',
            default => 'ESC'
        };

        $timestamp = now()->format('YmdHis');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

        return $prefix . '-' . $commandePrincipaleId . '-' . $timestamp . '-' . $random;
    }

    /**
     * Obtient le statut formaté pour l'affichage
     */
    public function getStatutFormateAttribute(): string
    {
        return match($this->statut) {
            'held' => 'Fonds retenus',
            'released' => 'Fonds libérés',
            'refunded' => 'Remboursé',
            'disputed' => 'En litige',
            'partial_refund' => 'Remboursement partiel',
            'expired' => 'Expiré',
            default => 'Inconnu'
        };
    }

    /**
     * Obtient la couleur du badge selon le statut
     */
    public function getStatutColorAttribute(): string
    {
        return match($this->statut) {
            'held' => 'warning',
            'released' => 'success',
            'refunded' => 'info',
            'disputed' => 'danger',
            'partial_refund' => 'secondary',
            'expired' => 'dark',
            default => 'light'
        };
    }

    /**
     * Obtient le montant formaté
     */
    public function getMontantFormateAttribute(): string
    {
        return number_format($this->montant_total, 0, ',', ' ') . ' ' . $this->devise;
    }

    /**
     * Scope pour les transactions éligibles à la libération
     */
    public function scopeEligibleForRelease($query)
    {
        return $query->where('statut', 'held')
                    ->where('en_litige', false)
                    ->whereHas('commandePrincipale', function ($q) {
                        $q->where('statut_global', 'TotalementLivré');
                    });
    }

    /**
     * Scope pour les transactions expirées
     */
    public function scopeExpired($query)
    {
        return $query->where('statut', 'held')
                    ->where('date_expiration', '<=', now());
    }

    /**
     * Scope pour les transactions en litige
     */
    public function scopeDisputed($query)
    {
        return $query->where('en_litige', true);
    }
}
