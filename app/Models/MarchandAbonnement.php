<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MarchandAbonnement extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'marchand_abonnements';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'marchand_id',
        'type_abonnement',
        'statut',
        'date_debut',
        'date_fin',
        'date_prochaine_facturation',
        'date_expiration_grace',
        'prix_mensuel',
        'commission_taux_min',
        'commission_taux_max',
        'reduction_logistique',
        'limite_produits',
        'limite_commandes_mois',
        'limite_campagnes_mois',
        'acces_analytics_avancees',
        'acces_support_prioritaire',
        'acces_gestionnaire_dedie',
        'acces_ia_predictive',
        'acces_evenements_exclusifs',
        'type_abonnement_precedent',
        'date_changement_abonnement',
        'raison_changement',
        'mode_facturation',
        'facturation_automatique',
        'methode_paiement_abonnement',
        'est_periode_essai',
        'fin_periode_essai',
        'code_promotion',
        'reduction_promotion',
        'derniere_notification_expiration',
        'nombre_rappels_paiement',
        'dernier_rappel_paiement',
        'fonctionnalites_activees',
        'limites_personnalisees',
        'notes_admin',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'date_debut' => 'datetime',
        'date_fin' => 'datetime',
        'date_prochaine_facturation' => 'datetime',
        'date_expiration_grace' => 'datetime',
        'date_changement_abonnement' => 'datetime',
        'fin_periode_essai' => 'datetime',
        'derniere_notification_expiration' => 'datetime',
        'dernier_rappel_paiement' => 'datetime',
        'prix_mensuel' => 'decimal:2',
        'commission_taux_min' => 'decimal:2',
        'commission_taux_max' => 'decimal:2',
        'reduction_logistique' => 'decimal:2',
        'reduction_promotion' => 'decimal:2',
        'facturation_automatique' => 'boolean',
        'acces_analytics_avancees' => 'boolean',
        'acces_support_prioritaire' => 'boolean',
        'acces_gestionnaire_dedie' => 'boolean',
        'acces_ia_predictive' => 'boolean',
        'acces_evenements_exclusifs' => 'boolean',
        'est_periode_essai' => 'boolean',
        'fonctionnalites_activees' => 'array',
        'limites_personnalisees' => 'array',
    ];

    /**
     * Relations
     */

    /**
     * Marchand propriétaire de l'abonnement
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Historique des changements d'abonnement
     */
    public function historique(): HasMany
    {
        return $this->hasMany(MarchandAbonnementHistorique::class, 'abonnement_id');
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Vérifie si l'abonnement est actif
     */
    public function estActif(): bool
    {
        return $this->statut === 'actif' &&
               ($this->date_fin === null || $this->date_fin->isFuture());
    }

    /**
     * Vérifie si l'abonnement est en période d'essai
     */
    public function estEnPeriodeEssai(): bool
    {
        return $this->est_periode_essai &&
               $this->fin_periode_essai &&
               $this->fin_periode_essai->isFuture();
    }

    /**
     * Vérifie si l'abonnement va expirer bientôt (dans les 7 jours)
     */
    public function vaExpirer(): bool
    {
        return $this->date_fin &&
               $this->date_fin->isFuture() &&
               $this->date_fin->diffInDays(now()) <= 7;
    }

    /**
     * Calcule le taux de commission pour un montant donné
     */
    public function calculerCommission(float $montant): float
    {
        if ($this->commission_taux_min === null || $this->commission_taux_max === null) {
            return 0;
        }

        // Logique simple : utiliser le taux minimum pour les petits montants,
        // le taux maximum pour les gros montants
        $seuil = 50000; // 50,000 FCFA

        if ($montant <= $seuil) {
            return ($montant * $this->commission_taux_max) / 100;
        } else {
            return ($montant * $this->commission_taux_min) / 100;
        }
    }

    /**
     * Obtient la configuration des fonctionnalités selon le type d'abonnement
     */
    public static function getConfigurationAbonnement(string $type): array
    {
        $configurations = [
            'gratuit' => [
                'prix_mensuel' => 0,
                'commission_taux_min' => 5.00,
                'commission_taux_max' => 10.00,
                'reduction_logistique' => 0,
                'limite_produits' => null,
                'limite_commandes_mois' => null,
                'limite_campagnes_mois' => 0,
                'acces_analytics_avancees' => false,
                'acces_support_prioritaire' => false,
                'acces_gestionnaire_dedie' => false,
                'acces_ia_predictive' => false,
                'acces_evenements_exclusifs' => false,
            ],
            'basique' => [
                'prix_mensuel' => 32797.85,
                'commission_taux_min' => 4.00,
                'commission_taux_max' => 8.00,
                'reduction_logistique' => 5.00,
                'limite_produits' => null,
                'limite_commandes_mois' => null,
                'limite_campagnes_mois' => 0,
                'acces_analytics_avancees' => false,
                'acces_support_prioritaire' => true,
                'acces_gestionnaire_dedie' => false,
                'acces_ia_predictive' => false,
                'acces_evenements_exclusifs' => false,
            ],
            'premium' => [
                'prix_mensuel' => 65595.70,
                'commission_taux_min' => 3.00,
                'commission_taux_max' => 6.00,
                'reduction_logistique' => 10.00,
                'limite_produits' => null,
                'limite_commandes_mois' => null,
                'limite_campagnes_mois' => 1,
                'acces_analytics_avancees' => true,
                'acces_support_prioritaire' => true,
                'acces_gestionnaire_dedie' => true,
                'acces_ia_predictive' => false,
                'acces_evenements_exclusifs' => false,
            ],
            'elite' => [
                'prix_mensuel' => 131191.40,
                'commission_taux_min' => 2.00,
                'commission_taux_max' => 4.00,
                'reduction_logistique' => 15.00,
                'limite_produits' => null,
                'limite_commandes_mois' => null,
                'limite_campagnes_mois' => 3,
                'acces_analytics_avancees' => true,
                'acces_support_prioritaire' => true,
                'acces_gestionnaire_dedie' => true,
                'acces_ia_predictive' => true,
                'acces_evenements_exclusifs' => true,
            ],
        ];

        return $configurations[$type] ?? $configurations['gratuit'];
    }

    /**
     * Crée un nouvel abonnement avec la configuration par défaut
     */
    public static function creerAbonnement(int $marchandId, string $type, array $options = []): self
    {
        $config = self::getConfigurationAbonnement($type);

        $abonnement = new self(array_merge($config, [
            'marchand_id' => $marchandId,
            'type_abonnement' => $type,
            'statut' => 'actif',
            'date_debut' => now(),
            'mode_facturation' => 'mensuel',
            'facturation_automatique' => true,
        ], $options));

        // Calculer la date de fin selon le mode de facturation
        if ($abonnement->mode_facturation === 'annuel') {
            $abonnement->date_fin = now()->addYear();
            $abonnement->date_prochaine_facturation = now()->addYear();
        } else {
            $abonnement->date_fin = now()->addMonth();
            $abonnement->date_prochaine_facturation = now()->addMonth();
        }

        $abonnement->save();

        return $abonnement;
    }
}
