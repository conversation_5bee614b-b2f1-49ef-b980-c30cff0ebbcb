<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class ClientMarchandConversation extends Model
{
    use HasFactory, HasUuids;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'client_marchand_conversations';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'client_id',
        'marchand_id',
        'commande_principale_id',
        'sous_commande_id',
        'produit_id',
        'sujet',
        'statut',
        'priorite',
        'type_conversation',
        'date_creation',
        'date_dernier_message',
        'date_fermeture',
        'cree_par_client_id',
        'ferme_par_client_id',
        'ferme_par_marchand_id',
        'nombre_messages_total',
        'nombre_messages_client',
        'nombre_messages_marchand',
        'messages_non_lus_client',
        'messages_non_lus_marchand',
        'metadata',
        'notification_client',
        'notification_marchand',
        'archivage_automatique',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date_creation' => 'datetime',
        'date_dernier_message' => 'datetime',
        'date_fermeture' => 'datetime',
        'nombre_messages_total' => 'integer',
        'nombre_messages_client' => 'integer',
        'nombre_messages_marchand' => 'integer',
        'messages_non_lus_client' => 'integer',
        'messages_non_lus_marchand' => 'integer',
        'metadata' => 'array',
        'notification_client' => 'boolean',
        'notification_marchand' => 'boolean',
        'archivage_automatique' => 'boolean',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'statut' => 'active',
        'priorite' => 'normale',
        'nombre_messages_total' => 0,
        'nombre_messages_client' => 0,
        'nombre_messages_marchand' => 0,
        'messages_non_lus_client' => 0,
        'messages_non_lus_marchand' => 0,
        'notification_client' => true,
        'notification_marchand' => true,
        'archivage_automatique' => true,
    ];

    /**
     * Relation avec le client
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Relation avec le marchand
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Relation avec la commande principale
     */
    public function commandePrincipale(): BelongsTo
    {
        return $this->belongsTo(CommandePrincipale::class);
    }

    /**
     * Relation avec la sous-commande
     */
    public function sousCommande(): BelongsTo
    {
        return $this->belongsTo(SousCommandeVendeur::class, 'sous_commande_id');
    }

    /**
     * Relation avec le produit
     */
    public function produit(): BelongsTo
    {
        return $this->belongsTo(Produit::class);
    }

    /**
     * Relation avec les messages
     */
    public function messages(): HasMany
    {
        return $this->hasMany(ConversationMessage::class, 'conversation_id');
    }

    /**
     * Relation avec le client créateur
     */
    public function creePar(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'cree_par_client_id');
    }

    /**
     * Crée une nouvelle conversation
     */
    public static function creerConversation(array $data): self
    {
        $conversation = static::create(array_merge($data, [
            'date_creation' => now(),
        ]));

        return $conversation;
    }

    /**
     * Ajoute un message à la conversation
     */
    public function ajouterMessage(array $messageData): ConversationMessage
    {
        $message = $this->messages()->create($messageData);

        // Mettre à jour les compteurs
        $this->increment('nombre_messages_total');

        if ($messageData['auteur_type'] === 'client') {
            $this->increment('nombre_messages_client');
            $this->increment('messages_non_lus_marchand');
        } elseif ($messageData['auteur_type'] === 'marchand') {
            $this->increment('nombre_messages_marchand');
            $this->increment('messages_non_lus_client');
        }

        // Mettre à jour la date du dernier message
        $this->update(['date_dernier_message' => now()]);

        return $message;
    }

    /**
     * Marque les messages comme lus par un participant
     */
    public function marquerCommeLu(string $participant): void
    {
        if ($participant === 'client') {
            $this->messages()
                ->where('lu_par_client', false)
                ->update([
                    'lu_par_client' => true,
                    'date_lecture_client' => now()
                ]);

            $this->update(['messages_non_lus_client' => 0]);

        } elseif ($participant === 'marchand') {
            $this->messages()
                ->where('lu_par_marchand', false)
                ->update([
                    'lu_par_marchand' => true,
                    'date_lecture_marchand' => now()
                ]);

            $this->update(['messages_non_lus_marchand' => 0]);
        }
    }

    /**
     * Ferme la conversation
     */
    public function fermer(string $fermePar, ?int $participantId = null): void
    {
        $updates = [
            'statut' => 'fermee',
            'date_fermeture' => now()
        ];

        if ($fermePar === 'client' && $participantId) {
            $updates['ferme_par_client_id'] = $participantId;
        } elseif ($fermePar === 'marchand' && $participantId) {
            $updates['ferme_par_marchand_id'] = $participantId;
        }

        $this->update($updates);

        // Ajouter un message système
        $this->ajouterMessage([
            'auteur_type' => 'system',
            'auteur_nom' => 'Système',
            'message' => "Conversation fermée par " . ($fermePar === 'client' ? 'le client' : 'le marchand'),
            'type_message' => 'system',
            'lu_par_client' => true,
            'lu_par_marchand' => true,
        ]);
    }

    /**
     * Vérifie si la conversation peut être rouverte
     */
    public function peutEtreRouverte(): bool
    {
        return $this->statut === 'fermee' &&
               $this->date_fermeture &&
               $this->date_fermeture->diffInDays(now()) <= 7;
    }

    /**
     * Rouvre la conversation
     */
    public function rouvrir(): void
    {
        if ($this->peutEtreRouverte()) {
            $this->update([
                'statut' => 'active',
                'date_fermeture' => null,
                'ferme_par_client_id' => null,
                'ferme_par_marchand_id' => null
            ]);

            // Ajouter un message système
            $this->ajouterMessage([
                'auteur_type' => 'system',
                'auteur_nom' => 'Système',
                'message' => 'Conversation rouverte',
                'type_message' => 'system',
                'lu_par_client' => true,
                'lu_par_marchand' => true,
            ]);
        }
    }

    /**
     * Obtient le statut formaté
     */
    public function getStatutFormateAttribute(): string
    {
        return match($this->statut) {
            'active' => 'Active',
            'fermee' => 'Fermée',
            'archivee' => 'Archivée',
            'bloquee' => 'Bloquée',
            default => 'Inconnu'
        };
    }

    /**
     * Obtient la couleur du badge selon le statut
     */
    public function getStatutColorAttribute(): string
    {
        return match($this->statut) {
            'active' => 'success',
            'fermee' => 'secondary',
            'archivee' => 'muted',
            'bloquee' => 'destructive',
            default => 'secondary'
        };
    }

    /**
     * Obtient le type formaté
     */
    public function getTypeFormateAttribute(): string
    {
        return match($this->type_conversation) {
            'question_produit' => 'Question sur produit',
            'probleme_commande' => 'Problème de commande',
            'demande_info' => 'Demande d\'information',
            'reclamation' => 'Réclamation',
            'autre' => 'Autre',
            default => 'Non spécifié'
        };
    }

    /**
     * Scope pour les conversations actives
     */
    public function scopeActives($query)
    {
        return $query->where('statut', 'active');
    }

    /**
     * Scope pour les conversations d'un client
     */
    public function scopeClient($query, int $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    /**
     * Scope pour les conversations d'un marchand
     */
    public function scopeMarchand($query, int $marchandId)
    {
        return $query->where('marchand_id', $marchandId);
    }

    /**
     * Scope pour les conversations avec messages non lus
     */
    public function scopeAvecMessagesNonLus($query, string $participant)
    {
        $field = $participant === 'client' ? 'messages_non_lus_client' : 'messages_non_lus_marchand';
        return $query->where($field, '>', 0);
    }

    /**
     * Scope pour les conversations récentes
     */
    public function scopeRecentes($query, int $jours = 30)
    {
        return $query->where('date_dernier_message', '>=', now()->subDays($jours));
    }
}
