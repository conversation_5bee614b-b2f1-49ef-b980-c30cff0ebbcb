<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class ConversationMessage extends Model
{
    use HasFactory, HasUuids;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'conversation_messages';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'conversation_id',
        'auteur_type',
        'auteur_client_id',
        'auteur_marchand_id',
        'auteur_nom',
        'message',
        'type_message',
        'pieces_jointes',
        'lu_par_client',
        'lu_par_marchand',
        'date_lecture_client',
        'date_lecture_marchand',
        'reponse_a_message_id',
        'important',
        'notification_envoyee',
        'archive',
        'metadata',
        'modere',
        'raison_moderation',
        'date_moderation',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'pieces_jointes' => 'array',
        'lu_par_client' => 'boolean',
        'lu_par_marchand' => 'boolean',
        'date_lecture_client' => 'datetime',
        'date_lecture_marchand' => 'datetime',
        'important' => 'boolean',
        'notification_envoyee' => 'boolean',
        'archive' => 'boolean',
        'metadata' => 'array',
        'modere' => 'boolean',
        'date_moderation' => 'datetime',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'type_message' => 'message',
        'lu_par_client' => false,
        'lu_par_marchand' => false,
        'important' => false,
        'notification_envoyee' => false,
        'archive' => false,
        'modere' => false,
    ];

    /**
     * Relation avec la conversation
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(ClientMarchandConversation::class, 'conversation_id');
    }

    /**
     * Relation avec le client auteur
     */
    public function auteurClient(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'auteur_client_id');
    }

    /**
     * Relation avec le marchand auteur
     */
    public function auteurMarchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class, 'auteur_marchand_id');
    }

    /**
     * Relation avec le message parent (réponse)
     */
    public function messageParent(): BelongsTo
    {
        return $this->belongsTo(ConversationMessage::class, 'reponse_a_message_id');
    }

    /**
     * Relation avec les réponses à ce message
     */
    public function reponses()
    {
        return $this->hasMany(ConversationMessage::class, 'reponse_a_message_id');
    }

    /**
     * Obtient l'auteur du message selon le type
     */
    public function getAuteurAttribute()
    {
        return match($this->auteur_type) {
            'client' => $this->auteurClient,
            'marchand' => $this->auteurMarchand,
            'system' => null,
            default => null
        };
    }

    /**
     * Marque le message comme lu par un participant
     */
    public function marquerCommeLu(string $participant): void
    {
        if ($participant === 'client' && !$this->lu_par_client) {
            $this->update([
                'lu_par_client' => true,
                'date_lecture_client' => now()
            ]);
        } elseif ($participant === 'marchand' && !$this->lu_par_marchand) {
            $this->update([
                'lu_par_marchand' => true,
                'date_lecture_marchand' => now()
            ]);
        }
    }

    /**
     * Vérifie si le message est lu par un participant
     */
    public function estLuPar(string $participant): bool
    {
        return match($participant) {
            'client' => $this->lu_par_client,
            'marchand' => $this->lu_par_marchand,
            default => false
        };
    }

    /**
     * Crée un message système
     */
    public static function creerMessageSysteme(
        string $conversationId, 
        string $message, 
        array $metadata = []
    ): self {
        return static::create([
            'conversation_id' => $conversationId,
            'auteur_type' => 'system',
            'auteur_nom' => 'Système',
            'message' => $message,
            'type_message' => 'system',
            'metadata' => $metadata,
            'lu_par_client' => true,
            'lu_par_marchand' => true,
        ]);
    }

    /**
     * Obtient le type de message formaté
     */
    public function getTypeMessageFormateAttribute(): string
    {
        return match($this->type_message) {
            'message' => 'Message',
            'question' => 'Question',
            'reponse' => 'Réponse',
            'information' => 'Information',
            'demande_action' => 'Demande d\'action',
            'confirmation' => 'Confirmation',
            'system' => 'Système',
            default => 'Message'
        };
    }

    /**
     * Obtient l'icône selon le type de message
     */
    public function getIconeAttribute(): string
    {
        return match($this->type_message) {
            'message' => 'message-circle',
            'question' => 'help-circle',
            'reponse' => 'message-square',
            'information' => 'info',
            'demande_action' => 'alert-circle',
            'confirmation' => 'check-circle',
            'system' => 'settings',
            default => 'message-circle'
        };
    }

    /**
     * Obtient la couleur selon l'auteur
     */
    public function getCouleurAuteurAttribute(): string
    {
        return match($this->auteur_type) {
            'client' => 'blue',
            'marchand' => 'green',
            'system' => 'gray',
            default => 'gray'
        };
    }

    /**
     * Vérifie si le message peut être modifié
     */
    public function peutEtreModifie(): bool
    {
        return $this->auteur_type !== 'system' && 
               $this->created_at->diffInMinutes(now()) <= 15 &&
               !$this->modere;
    }

    /**
     * Vérifie si le message peut être supprimé
     */
    public function peutEtreSupprime(): bool
    {
        return $this->auteur_type !== 'system' && 
               $this->created_at->diffInHours(now()) <= 24 &&
               !$this->modere;
    }

    /**
     * Scope pour les messages non lus par un participant
     */
    public function scopeNonLusPar($query, string $participant)
    {
        $field = $participant === 'client' ? 'lu_par_client' : 'lu_par_marchand';
        return $query->where($field, false);
    }

    /**
     * Scope pour les messages d'un type d'auteur
     */
    public function scopeAuteurType($query, string $type)
    {
        return $query->where('auteur_type', $type);
    }

    /**
     * Scope pour les messages importants
     */
    public function scopeImportants($query)
    {
        return $query->where('important', true);
    }

    /**
     * Scope pour les messages récents
     */
    public function scopeRecents($query, int $heures = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($heures));
    }

    /**
     * Scope pour les messages avec pièces jointes
     */
    public function scopeAvecPiecesJointes($query)
    {
        return $query->whereNotNull('pieces_jointes')
                    ->where('pieces_jointes', '!=', '[]');
    }
}
