<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Translatable\HasTranslations;

class Produit extends Model
{
    use HasFactory, HasTranslations;

    /**
     * Les attributs qui sont traduisibles.
     *
     * @var array
     */
    public $translatable = ['nom', 'description'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'marchand_id',
        'categorie_id',
        'nom',
        'description',
        'prix',
        'stock', // Corrigé : utilise 'stock' au lieu de 'quantite'
        'images',
        'slug',
        'statut',
        'featured',
        'currency',
        'product_code',
        'marque', // Corrigé : utilise 'marque' au lieu de 'brand'
        'attributs',
        'poids',
        'discount_price',
        'discount_start_date',
        'discount_end_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'images' => 'array',
        'prix' => 'decimal:2',
        'stock' => 'integer', // Corrigé : utilise 'stock' au lieu de 'quantite'
        'featured' => 'boolean',
        'attributs' => 'array',
        'discount_price' => 'decimal:2',
        'discount_start_date' => 'datetime',
        'discount_end_date' => 'datetime',
        'creeLe' => 'datetime',
        'misAJourLe' => 'datetime',
        'poids' => 'decimal:2',
    ];

    /**
     * Relations
     */

    /**
     * Marchand propriétaire du produit
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Catégorie du produit
     */
    public function categorie(): BelongsTo
    {
        return $this->belongsTo(Categorie::class);
    }

    /**
     * Variantes du produit
     */
    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class, 'produit_id');
    }

    /**
     * Alias pour la compatibilité (méthode française)
     */
    public function variantes(): HasMany
    {
        return $this->variants();
    }

    /**
     * Avis sur le produit
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Tailles disponibles pour ce produit
     */
    public function sizes()
    {
        return $this->belongsToMany(Size::class, 'produit_size')
            ->withTimestamps();
    }

    /**
     * Zones de livraison configurées pour ce produit
     */
    public function produitZonesLivraison(): HasMany
    {
        return $this->hasMany(ProduitZoneLivraison::class, 'produit_id');
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Vérifie si le produit est en stock
     */
    public function estEnStock(): bool
    {
        return $this->stock > 0; // Corrigé : utilise 'stock' au lieu de 'quantite'
    }

    /**
     * Accesseur pour la compatibilité avec l'ancien nom 'quantite'
     */
    public function getQuantiteAttribute(): int
    {
        return $this->stock;
    }

    /**
     * Mutateur pour la compatibilité avec l'ancien nom 'quantite'
     */
    public function setQuantiteAttribute($value): void
    {
        $this->attributes['stock'] = $value;
    }

    /**
     * Accesseur pour la compatibilité avec l'ancien nom 'brand'
     */
    public function getBrandAttribute(): ?string
    {
        return $this->marque;
    }

    /**
     * Mutateur pour la compatibilité avec l'ancien nom 'brand'
     */
    public function setBrandAttribute($value): void
    {
        $this->attributes['marque'] = $value;
    }

    /**
     * Obtient le prix formaté
     */
    public function getPrixFormate(): string
    {
        return number_format($this->prix, 2) . ' ' . ($this->currency ?? 'FCFA');
    }

    /**
     * Vérifie si le produit est mis en avant
     */
    public function estFeatured(): bool
    {
        return $this->featured === true;
    }

    /**
     * Obtient l'URL de la première image
     */
    public function getPremierImage(): ?string
    {
        if (empty($this->images)) {
            return null;
        }

        return $this->images[0] ?? null;
    }

    /**
     * Vérifie si le produit est actuellement en promotion
     *
     * @return bool
     */
    public function getIsOnDiscountAttribute(): bool
    {
        if (empty($this->discount_price) || $this->discount_price >= $this->prix) {
            return false;
        }

        $now = now();

        try {
            // Vérifier les dates de début et fin de promotion
            if ($this->discount_start_date && $now->lt($this->discount_start_date)) {
                return false;
            }

            if ($this->discount_end_date && $now->gt($this->discount_end_date)) {
                return false;
            }
        } catch (\Exception $e) {
            // Si il y a une erreur de parsing des dates, considérer que le discount n'est pas actif
            \Illuminate\Support\Facades\Log::warning("Erreur de parsing des dates de discount pour le produit {$this->id}: " . $e->getMessage());
            return false;
        }

        return true;
    }
}
