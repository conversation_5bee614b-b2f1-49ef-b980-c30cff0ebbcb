<?php

namespace App\Console\Commands;

use App\Models\Produit;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixDiscountDates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:discount-dates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix malformed discount dates in products table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Vérification et correction des dates de discount...');

        $problematicProducts = [];
        $fixedCount = 0;

        // Récupérer tous les produits avec des dates de discount
        $products = Produit::whereNotNull('discount_start_date')
            ->orWhereNotNull('discount_end_date')
            ->get();

        $this->info("Vérification de {$products->count()} produits...");

        foreach ($products as $product) {
            $hasIssue = false;
            $fixes = [];

            // Vérifier discount_start_date
            if ($product->discount_start_date) {
                try {
                    // Essayer d'accéder à la date pour déclencher l'erreur si elle existe
                    $startDate = $product->discount_start_date;
                    if ($startDate) {
                        $startDate->format('Y-m-d H:i:s');
                    }
                } catch (\Exception $e) {
                    $hasIssue = true;
                    $rawValue = $product->getAttributes()['discount_start_date'];
                    
                    // Si c'est un timestamp Unix, le convertir
                    if (is_numeric($rawValue)) {
                        $timestamp = (int) $rawValue;
                        // Vérifier si c'est en secondes ou millisecondes
                        if ($timestamp > 9999999999) {
                            $timestamp = $timestamp / 1000;
                        }
                        $fixes['discount_start_date'] = date('Y-m-d H:i:s', $timestamp);
                    } else {
                        // Sinon, mettre à null
                        $fixes['discount_start_date'] = null;
                    }
                }
            }

            // Vérifier discount_end_date
            if ($product->discount_end_date) {
                try {
                    $endDate = $product->discount_end_date;
                    if ($endDate) {
                        $endDate->format('Y-m-d H:i:s');
                    }
                } catch (\Exception $e) {
                    $hasIssue = true;
                    $rawValue = $product->getAttributes()['discount_end_date'];
                    
                    // Si c'est un timestamp Unix, le convertir
                    if (is_numeric($rawValue)) {
                        $timestamp = (int) $rawValue;
                        // Vérifier si c'est en secondes ou millisecondes
                        if ($timestamp > 9999999999) {
                            $timestamp = $timestamp / 1000;
                        }
                        $fixes['discount_end_date'] = date('Y-m-d H:i:s', $timestamp);
                    } else {
                        // Sinon, mettre à null
                        $fixes['discount_end_date'] = null;
                    }
                }
            }

            if ($hasIssue) {
                $problematicProducts[] = [
                    'id' => $product->id,
                    'nom' => $product->nom,
                    'fixes' => $fixes
                ];

                // Appliquer les corrections directement en base
                if (!empty($fixes)) {
                    DB::table('produits')
                        ->where('id', $product->id)
                        ->update($fixes);
                    $fixedCount++;
                    
                    $this->line("✓ Produit {$product->id} ({$product->nom}) corrigé");
                }
            }
        }

        if (empty($problematicProducts)) {
            $this->info('✅ Aucun problème de date trouvé !');
        } else {
            $this->info("🔧 {$fixedCount} produits corrigés :");
            
            foreach ($problematicProducts as $product) {
                $this->line("- Produit {$product['id']}: {$product['nom']}");
                foreach ($product['fixes'] as $field => $value) {
                    $displayValue = $value ? $value : 'NULL';
                    $this->line("  {$field}: {$displayValue}");
                }
            }
        }

        $this->info('✅ Correction terminée !');
        
        return 0;
    }
}
