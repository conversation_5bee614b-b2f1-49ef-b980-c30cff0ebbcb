<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\EscrowService;
use Illuminate\Support\Facades\Log;

class EscrowReleaseExpired extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'escrow:release-expired 
                            {--dry-run : Afficher les transactions éligibles sans les traiter}
                            {--limit=50 : Nombre maximum de transactions à traiter}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Libère automatiquement les fonds escrow dont le délai de contestation est expiré';

    /**
     * Service d\'escrow
     */
    protected EscrowService $escrowService;

    /**
     * Create a new command instance.
     */
    public function __construct(EscrowService $escrowService)
    {
        parent::__construct();
        $this->escrowService = $escrowService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔄 Démarrage de la libération automatique des fonds escrow...');

        try {
            // Mettre à jour les dates d'expiration d'abord
            $this->info('📅 Mise à jour des dates d\'expiration...');
            $updateResult = $this->escrowService->updateExpirationDates();
            
            if ($updateResult['success']) {
                $this->info("✅ {$updateResult['updated_count']} dates d'expiration mises à jour");
            } else {
                $this->error("❌ Erreur lors de la mise à jour des dates: {$updateResult['error']}");
                return Command::FAILURE;
            }

            // Traiter les libérations éligibles
            $this->info('🚀 Traitement des libérations éligibles...');
            
            if ($this->option('dry-run')) {
                $this->handleDryRun();
            } else {
                $this->handleActualRelease();
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Erreur fatale: {$e->getMessage()}");
            Log::error('Erreur dans escrow:release-expired', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Gère le mode dry-run (simulation)
     */
    private function handleDryRun(): void
    {
        $this->warn('🔍 MODE DRY-RUN - Aucune modification ne sera effectuée');

        $eligibleTransactions = \App\Models\EscrowTransaction::eligibleForRelease()
            ->with(['commandePrincipale'])
            ->limit($this->option('limit'))
            ->get();

        if ($eligibleTransactions->isEmpty()) {
            $this->info('✅ Aucune transaction éligible pour libération');
            return;
        }

        $this->info("📋 {$eligibleTransactions->count()} transaction(s) éligible(s) pour libération:");

        $headers = ['ID Escrow', 'Commande', 'Montant', 'Méthode', 'Date Hold', 'Statut'];
        $rows = [];

        foreach ($eligibleTransactions as $transaction) {
            $rows[] = [
                substr($transaction->id, 0, 8) . '...',
                $transaction->commandePrincipale->numero_commande,
                $transaction->montant_formate,
                strtoupper($transaction->payment_method),
                $transaction->date_hold->format('d/m/Y H:i'),
                $transaction->statut_formate
            ];
        }

        $this->table($headers, $rows);

        $totalMontant = $eligibleTransactions->sum('montant_total');
        $this->info("💰 Montant total à libérer: " . number_format($totalMontant, 0, ',', ' ') . " FCFA");
    }

    /**
     * Gère la libération effective
     */
    private function handleActualRelease(): void
    {
        $result = $this->escrowService->processEligibleReleases();

        if (!$result['success']) {
            $this->error("❌ Erreur lors du traitement: {$result['error']}");
            return;
        }

        $this->info("📊 Résultats du traitement:");
        $this->info("   • Transactions éligibles: {$result['eligible_count']}");
        $this->info("   • Transactions traitées: " . count($result['processed']));
        $this->info("   • Erreurs: " . count($result['errors']));

        if (!empty($result['processed'])) {
            $this->info("✅ Transactions libérées avec succès:");
            
            $headers = ['ID Escrow', 'Commande', 'Montant Libéré'];
            $rows = [];

            foreach ($result['processed'] as $processed) {
                $rows[] = [
                    substr($processed['escrow_transaction_id'], 0, 8) . '...',
                    $processed['commande_principale_id'],
                    number_format($processed['montant_libere'], 0, ',', ' ') . ' FCFA'
                ];
            }

            $this->table($headers, $rows);

            $totalLibere = array_sum(array_column($result['processed'], 'montant_libere'));
            $this->info("💰 Montant total libéré: " . number_format($totalLibere, 0, ',', ' ') . " FCFA");
        }

        if (!empty($result['errors'])) {
            $this->error("❌ Erreurs rencontrées:");
            foreach ($result['errors'] as $error) {
                $this->error("   • Escrow {$error['escrow_transaction_id']}: {$error['error']}");
            }
        }

        // Afficher les statistiques finales
        $this->displayStats();
    }

    /**
     * Affiche les statistiques escrow
     */
    private function displayStats(): void
    {
        $this->info('📈 Statistiques Escrow:');
        
        $statsResult = $this->escrowService->getEscrowStats();
        
        if ($statsResult['success']) {
            $stats = $statsResult['stats'];
            
            $this->info("   • Fonds retenus: " . number_format($stats['total_held'], 0, ',', ' ') . " FCFA ({$stats['count_held']} transactions)");
            $this->info("   • Fonds libérés: " . number_format($stats['total_released'], 0, ',', ' ') . " FCFA ({$stats['count_released']} transactions)");
            $this->info("   • Fonds remboursés: " . number_format($stats['total_refunded'], 0, ',', ' ') . " FCFA ({$stats['count_refunded']} transactions)");
            $this->info("   • Transactions en litige: {$stats['count_disputed']}");
            $this->info("   • Éligibles pour libération: {$stats['count_eligible_for_release']}");
            $this->info("   • Délai expiré: {$stats['count_expired']}");
        }
    }
}
