<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MeilisearchService;
use Illuminate\Support\Facades\Log;

class InitializeMeilisearch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meilisearch:init 
                            {--index-all : Indexe également tous les produits et catégories}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialise Meilisearch avec les index et configurations nécessaires';

    protected MeilisearchService $meilisearchService;

    public function __construct(MeilisearchService $meilisearchService)
    {
        parent::__construct();
        $this->meilisearchService = $meilisearchService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Initialisation de Meilisearch...');

        try {
            // Initialiser les index
            $this->info('🔧 Configuration des index...');
            $this->meilisearchService->initializeIndexes();
            $this->info('✅ Index configurés avec succès');

            // Indexer toutes les données si demandé
            if ($this->option('index-all')) {
                $this->info('📦 Indexation de tous les produits...');
                $this->meilisearchService->indexAllProduits();
                $this->info('✅ Produits indexés');

                $this->info('📂 Indexation de toutes les catégories...');
                $this->meilisearchService->indexAllCategories();
                $this->info('✅ Catégories indexées');
            }
            
            $this->info('🎉 Meilisearch initialisé avec succès !');
            
            if (!$this->option('index-all')) {
                $this->warn('💡 Pour indexer les données existantes, utilisez :');
                $this->line('   php artisan meilisearch:index-products');
                $this->line('   php artisan meilisearch:index-categories');
                $this->line('   ou utilisez --index-all avec cette commande');
            }
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de l\'initialisation de Meilisearch: ' . $e->getMessage());
            Log::error('Erreur lors de l\'initialisation de Meilisearch', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return Command::FAILURE;
        }
    }
}
