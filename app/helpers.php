<?php

if (!function_exists('cdn_image_url')) {
    /**
     * Génère l'URL complète pour une image en utilisant le CDN configuré
     *
     * @param string|null $imagePath Le chemin relatif de l'image
     * @param string $baseDir Le dossier de base (ex: 'products', 'categories', 'banners')
     * @return string|null L'URL complète de l'image ou null si pas d'image
     */
    function cdn_image_url(?string $imagePath, string $baseDir = 'products'): ?string
    {
        return \App\Helpers\ImageUrlHelper::getImageUrl($imagePath, $baseDir);
    }
}

if (!function_exists('cdn_thumbnail_url')) {
    /**
     * Génère l'URL d'une miniature en utilisant le CDN configuré
     *
     * @param string|null $imagePath Le chemin de l'image originale
     * @param string $size La taille de la miniature (small, medium, large)
     * @param string $baseDir Le dossier de base
     * @return string|null L'URL de la miniature
     */
    function cdn_thumbnail_url(?string $imagePath, string $size = 'medium', string $baseDir = 'products'): ?string
    {
        return \App\Helpers\ImageUrlHelper::getThumbnailUrl($imagePath, $size, $baseDir);
    }
}

if (!function_exists('cdn_product_image_url')) {
    /**
     * Génère l'URL d'une image de produit avec la logique de dossier basée sur l'ID
     *
     * @param string|null $imagePath Le chemin de l'image
     * @param int|null $productId L'ID du produit
     * @return string|null L'URL de l'image
     */
    function cdn_product_image_url(?string $imagePath, ?int $productId = null): ?string
    {
        return \App\Helpers\ImageUrlHelper::getProductImageUrl($imagePath, $productId);
    }
}


